#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检测器集成测试脚本
测试RKNN检测器和YOLO检测器的集成
"""

import sys
import os
import logging

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_detector_import():
    """测试检测器导入"""
    print("=== 检测器导入测试 ===")
    
    try:
        from src.garbage_sorter.detection.rknn_detector import RKNNDetector
        print("✅ RKNN检测器导入成功")
        rknn_available = True
    except ImportError as e:
        print(f"❌ RKNN检测器导入失败: {e}")
        rknn_available = False
    
    # YOLO检测器已移除，只使用RKNN
    yolo_available = False
    print("ℹ️  YOLO检测器已移除，系统完全使用RKNN")

    return rknn_available, yolo_available

def test_detector_initialization():
    """测试检测器初始化"""
    print("\n=== 检测器初始化测试 ===")
    
    rknn_available, yolo_available = test_detector_import()
    
    # 测试RKNN检测器
    if rknn_available:
        try:
            from src.garbage_sorter.detection.rknn_detector import RKNNDetector
            rknn_detector = RKNNDetector(model_name="rknn-0804", conf_thres=0.4)
            print("✅ RKNN检测器初始化成功")
            print(f"   - 模型加载状态: {rknn_detector.is_model_loaded()}")
            print(f"   - 类别映射: {list(rknn_detector.get_class_mapping().keys())}")
        except Exception as e:
            print(f"❌ RKNN检测器初始化失败: {e}")
    
    # 测试RKNN检测器
    if rknn_available:
        try:
            from src.garbage_sorter.detection.rknn_detector import RKNNDetector
            rknn_detector = RKNNDetector(model_name="detect/rknn-0804.rknn", conf_thres=0.4)
            print("✅ RKNN检测器初始化成功")
            print(f"   - 模型加载状态: {rknn_detector.is_loaded}")
            print(f"   - 类别映射: {list(rknn_detector.class_mapping.keys())}")
        except Exception as e:
            print(f"❌ RKNN检测器初始化失败: {e}")

def test_gui_integration():
    """测试GUI集成"""
    print("\n=== GUI集成测试 ===")
    
    try:
        import tkinter as tk
        from src.garbage_sorter.gui.gui_client import GarbageSorterGUI
        
        # 创建测试窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        
        # 初始化GUI（不显示）
        gui = GarbageSorterGUI(root)
        
        print("✅ GUI初始化成功")
        print(f"   - 检测器类型: {getattr(gui, 'detector_type', 'Unknown')}")
        print(f"   - 检测器对象: {type(gui.detector).__name__}")

        # 测试检测器接口兼容性
        if hasattr(gui.detector, 'detect_frame'):
            print("✅ detect_frame方法可用")
        else:
            print("❌ detect_frame方法不可用")

        if hasattr(gui.detector, 'get_last_detection'):
            print("✅ get_last_detection方法可用")
        else:
            print("❌ get_last_detection方法不可用")
        
        root.destroy()
        
    except Exception as e:
        print(f"❌ GUI集成测试失败: {e}")

def test_config_loading():
    """测试配置加载"""
    print("\n=== 配置加载测试 ===")
    
    try:
        import json
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        print("✅ 配置文件加载成功")
        
        if 'detection' in config:
            detection_config = config['detection']
            print(f"   - 检测器类型: {detection_config.get('detector_type', 'auto')}")
            print(f"   - 置信度阈值: {detection_config.get('confidence_threshold', 0.4)}")
            # print(f"   - YOLO模型: {detection_config.get('yolo_model', 'yolov5s1')}")  # YOLO已移除
            print(f"   - RKNN模型: {detection_config.get('rknn_model', 'rknn-0804.rknn')}")
            print(f"   - 优先使用RKNN: {detection_config.get('use_rknn_if_available', True)}")
        else:
            print("❌ 检测配置部分缺失")
            
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")

def main():
    """主测试函数"""
    print("🚀 垃圾分拣系统 - 检测器集成测试")
    print("=" * 50)
    
    test_config_loading()
    test_detector_import()
    test_detector_initialization()
    test_gui_integration()
    
    print("\n" + "=" * 50)
    print("✨ 测试完成！")
    print("\n使用说明:")
    print("1. 如果RKNN检测器可用，系统会自动使用RKNN进行高性能检测")
    print("2. 如果RKNN不可用，系统会回退到YOLO检测器")
    print("3. 可以通过config.json中的detector_type配置强制指定检测器类型")
    print("4. 两种检测器使用相同的接口，无需修改其他代码")

if __name__ == "__main__":
    main()