#!/usr/bin/env python3
"""
垃圾分拣系统实际运行验证
"""

import os
import sys
import time
import traceback
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

def print_header(title):
    """打印测试标题"""
    print("\n" + "="*60)
    print(f"🧪 {title}")
    print("="*60)

def print_section(title):
    """打印章节标题"""
    print(f"\n📋 {title}")
    print("-" * 40)

def test_basic_environment():
    """测试基础环境"""
    print_header("基础环境验证")
    
    results = {}
    
    # 1. Python版本
    print_section("Python环境")
    python_version = sys.version
    print(f"Python版本: {python_version}")
    results['python_version'] = python_version
    
    # 2. 必需文件检查
    print_section("关键文件检查")
    critical_files = [
        "src/garbage_sorter/gui/gui_client.py",
        "src/garbage_sorter/detection/rknn_detector.py", 
        "detect/rknn-0804.rknn"
    ]
    
    for file_path in critical_files:
        exists = os.path.exists(file_path)
        status = "✅" if exists else "❌"
        print(f"{status} {file_path}")
        results[f"file_{file_path}"] = exists
    
    # 3. 依赖包检查
    print_section("依赖包检查")
    dependencies = [
        ("cv2", "OpenCV"),
        ("numpy", "NumPy"), 
        ("tkinter", "Tkinter"),
        ("PIL", "Pillow")
    ]
    
    for module, name in dependencies:
        try:
            __import__(module)
            print(f"✅ {name}")
            results[f"dep_{module}"] = True
        except ImportError as e:
            print(f"❌ {name}: {e}")
            results[f"dep_{module}"] = False
    
    return results

def test_gui_client_fixes():
    """测试GUI客户端修复"""
    print_header("GUI客户端修复验证")
    
    results = {}
    
    try:
        # 读取GUI客户端源码
        gui_file = "src/garbage_sorter/gui/gui_client.py"
        with open(gui_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查修复1: 掉落检测触发机制
        print_section("修复1: 掉落检测触发机制")
        drop_features = [
            ("waiting_for_drop", "等待掉落状态变量"),
            ("drop_detection_enabled", "掉落检测启用变量"),
            ("on_drop_detected", "掉落检测回调函数"),
            ("等待垃圾投放检测触发", "等待提示信息")
        ]
        
        drop_score = 0
        for feature, description in drop_features:
            found = feature in content
            status = "✅" if found else "❌"
            print(f"{status} {description}")
            if found:
                drop_score += 1
            results[f"drop_{feature}"] = found
        
        print(f"掉落检测机制完成度: {drop_score}/{len(drop_features)} ({drop_score/len(drop_features)*100:.1f}%)")
        
        # 检查修复2: ROI坐标系转换
        print_section("修复2: ROI坐标系转换")
        roi_features = [
            ("convert_full_to_roi_coords", "坐标转换函数"),
            ("is_bbox_in_roi", "ROI范围检查函数"),
            ("draw_detection_results_for_roi", "ROI检测结果绘制"),
            ("修复2", "修复标记注释")
        ]
        
        roi_score = 0
        for feature, description in roi_features:
            found = feature in content
            status = "✅" if found else "❌"
            print(f"{status} {description}")
            if found:
                roi_score += 1
            results[f"roi_{feature}"] = found
        
        print(f"ROI坐标转换完成度: {roi_score}/{len(roi_features)} ({roi_score/len(roi_features)*100:.1f}%)")
        
        # 总体评估
        total_features = len(drop_features) + len(roi_features)
        total_score = drop_score + roi_score
        overall_completion = (total_score / total_features) * 100
        
        print_section("修复总体评估")
        print(f"总体完成度: {total_score}/{total_features} ({overall_completion:.1f}%)")
        
        if overall_completion >= 90:
            print("✅ 修复状态: 优秀")
        elif overall_completion >= 70:
            print("⚠️ 修复状态: 良好")
        else:
            print("❌ 修复状态: 需要改进")
        
        results['overall_completion'] = overall_completion
        
    except Exception as e:
        print(f"❌ GUI客户端检查异常: {e}")
        results['exception'] = str(e)
    
    return results

def test_coordinate_conversion():
    """测试坐标转换功能"""
    print_header("坐标转换功能测试")
    
    results = {}
    
    # 实现坐标转换函数
    def convert_full_to_roi_coords(bbox, roi_area):
        """将完整帧坐标转换为ROI相对坐标"""
        if len(bbox) != 4:
            return bbox
        roi_x1, roi_y1, roi_x2, roi_y2 = roi_area
        det_x1, det_y1, det_x2, det_y2 = bbox
        return [det_x1 - roi_x1, det_y1 - roi_y1, det_x2 - roi_x1, det_y2 - roi_y1]
    
    def is_bbox_in_roi(bbox, roi_area):
        """检查检测框是否在ROI区域内"""
        if len(bbox) != 4:
            return False
        roi_x1, roi_y1, roi_x2, roi_y2 = roi_area
        det_x1, det_y1, det_x2, det_y2 = bbox
        return not (det_x2 <= roi_x1 or det_x1 >= roi_x2 or 
                   det_y2 <= roi_y1 or det_y1 >= roi_y2)
    
    # 测试用例
    roi_area = (0, 120, 640, 360)
    test_cases = [
        {
            "name": "中心检测框",
            "bbox": [300, 200, 340, 240],
            "expected_in_roi": True
        },
        {
            "name": "左边界检测框", 
            "bbox": [10, 150, 50, 190],
            "expected_in_roi": True
        },
        {
            "name": "右边界检测框",
            "bbox": [590, 300, 630, 340], 
            "expected_in_roi": True
        },
        {
            "name": "上方外部检测框",
            "bbox": [300, 50, 340, 90],
            "expected_in_roi": False
        },
        {
            "name": "下方外部检测框",
            "bbox": [300, 400, 340, 440],
            "expected_in_roi": False
        }
    ]
    
    print_section("坐标转换测试用例")
    print(f"ROI区域: {roi_area}")
    print()
    
    passed_tests = 0
    total_tests = len(test_cases)
    
    for i, case in enumerate(test_cases, 1):
        bbox = case['bbox']
        expected_in_roi = case['expected_in_roi']
        
        # 执行测试
        actual_in_roi = is_bbox_in_roi(bbox, roi_area)
        roi_coords = convert_full_to_roi_coords(bbox, roi_area)
        
        # 验证结果
        test_passed = actual_in_roi == expected_in_roi
        status = "✅" if test_passed else "❌"
        
        print(f"{status} 测试{i}: {case['name']}")
        print(f"   原始坐标: {bbox}")
        print(f"   ROI检查: {actual_in_roi} (期望: {expected_in_roi})")
        print(f"   ROI坐标: {roi_coords}")
        
        if test_passed:
            passed_tests += 1
        
        results[f"test_{i}"] = test_passed
    
    success_rate = (passed_tests / total_tests) * 100
    print(f"\n📊 坐标转换测试结果:")
    print(f"   通过: {passed_tests}/{total_tests}")
    print(f"   成功率: {success_rate:.1f}%")
    
    results['success_rate'] = success_rate
    results['passed_tests'] = passed_tests
    results['total_tests'] = total_tests
    
    return results

def test_rknn_model():
    """测试RKNN模型"""
    print_header("RKNN模型验证")
    
    results = {}
    
    print_section("模型文件检查")
    model_path = "detect/rknn-0804.rknn"
    
    if os.path.exists(model_path):
        file_size = os.path.getsize(model_path)
        file_size_mb = file_size / (1024 * 1024)
        
        print(f"✅ 模型文件存在: {model_path}")
        print(f"✅ 文件大小: {file_size_mb:.2f} MB")
        
        if file_size > 0:
            print(f"✅ 文件非空")
            results['model_file_valid'] = True
        else:
            print(f"❌ 文件为空")
            results['model_file_valid'] = False
    else:
        print(f"❌ 模型文件不存在: {model_path}")
        results['model_file_valid'] = False
    
    # 检查RKNN可用性
    print_section("RKNN环境检查")
    try:
        from rknnlite.api import RKNNLite
        print("✅ RKNN Lite可用")
        results['rknn_available'] = True
    except ImportError:
        print("❌ RKNN Lite不可用")
        results['rknn_available'] = False
    
    return results

def generate_final_report(all_results):
    """生成最终报告"""
    print_header("系统验证最终报告")
    
    # 统计各部分结果
    sections = {
        'environment': '基础环境',
        'gui_fixes': 'GUI修复',
        'coordinate': '坐标转换', 
        'rknn': 'RKNN模型'
    }
    
    section_scores = {}
    
    for section_key, section_name in sections.items():
        if section_key in all_results:
            section_data = all_results[section_key]
            
            if section_key == 'gui_fixes':
                score = section_data.get('overall_completion', 0)
            elif section_key == 'coordinate':
                score = section_data.get('success_rate', 0)
            else:
                # 计算布尔值的成功率
                bool_values = [v for v in section_data.values() if isinstance(v, bool)]
                score = (sum(bool_values) / len(bool_values) * 100) if bool_values else 0
            
            section_scores[section_name] = score
    
    print_section("各部分评估结果")
    overall_score = 0
    for section_name, score in section_scores.items():
        status = "✅" if score >= 80 else "⚠️" if score >= 60 else "❌"
        print(f"{status} {section_name}: {score:.1f}%")
        overall_score += score
    
    overall_score = overall_score / len(section_scores) if section_scores else 0
    
    print_section("系统整体评估")
    print(f"整体得分: {overall_score:.1f}%")
    
    if overall_score >= 85:
        status = "✅ 优秀"
        recommendation = "系统状态良好，可以正常运行"
    elif overall_score >= 70:
        status = "⚠️ 良好"
        recommendation = "系统基本可用，建议修复低分项"
    elif overall_score >= 50:
        status = "⚠️ 一般"
        recommendation = "系统存在问题，需要重点修复"
    else:
        status = "❌ 较差"
        recommendation = "系统存在严重问题，需要全面检查"
    
    print(f"系统状态: {status}")
    print(f"建议: {recommendation}")
    
    # 具体建议
    print_section("具体改进建议")
    
    for section_name, score in section_scores.items():
        if score < 80:
            if section_name == '基础环境':
                print("📦 检查并安装缺失的依赖包")
            elif section_name == 'GUI修复':
                print("🔧 完善GUI客户端的修复实现")
            elif section_name == '坐标转换':
                print("📐 检查坐标转换逻辑实现")
            elif section_name == 'RKNN模型':
                print("🤖 检查RKNN环境和模型文件")
    
    return {
        'overall_score': overall_score,
        'section_scores': section_scores,
        'status': status,
        'recommendation': recommendation
    }

def main():
    """主函数"""
    print("🚀 垃圾分拣系统全面验证测试")
    print("时间:", time.strftime("%Y-%m-%d %H:%M:%S"))
    
    all_results = {}
    
    try:
        # 执行各项测试
        all_results['environment'] = test_basic_environment()
        all_results['gui_fixes'] = test_gui_client_fixes()
        all_results['coordinate'] = test_coordinate_conversion()
        all_results['rknn'] = test_rknn_model()
        
        # 生成最终报告
        final_report = generate_final_report(all_results)
        
        return final_report
        
    except Exception as e:
        print(f"❌ 测试执行异常: {e}")
        traceback.print_exc()
        return None

if __name__ == "__main__":
    main()
