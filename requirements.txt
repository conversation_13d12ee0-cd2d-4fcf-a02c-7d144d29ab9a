# 垃圾分拣系统依赖包

# 核心依赖
pyserial>=3.5         # 串口通信
numpy>=1.21.0         # 数值计算

# GUI相关
tkinter               # GUI界面（Python内置，某些系统需要单独安装）

# 图像和视频处理
opencv-python>=4.5.0  # 视频播放和图像处理
Pillow>=8.0.0         # 图像处理

# RKNN检测（Orange Pi专用）
# rknn-toolkit-lite2   # RKNN推理库（需要在Orange Pi上安装）

# 原YOLO依赖已移除，系统完全使用RKNN
# torch>=1.9.0          # PyTorch（已移除）
# torchvision>=0.10.0   # PyTorch视觉库（已移除）
# ultralytics>=8.0.0    # YOLOv8（已移除）

# 开发和测试工具（可选）
# pytest>=6.0.0       # 测试框架（取消注释以启用）
# pytest-cov>=2.0.0   # 测试覆盖率（取消注释以启用）
