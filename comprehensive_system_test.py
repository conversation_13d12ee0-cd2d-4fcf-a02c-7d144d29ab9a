#!/usr/bin/env python3
"""
垃圾分拣系统全面验证测试框架
"""

import sys
import os
import time
import threading
import cv2
import numpy as np
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent / "src"))

class SystemTestFramework:
    def __init__(self):
        self.test_results = {}
        self.errors = []
        self.warnings = []
        
    def log_result(self, test_name, status, details=""):
        """记录测试结果"""
        self.test_results[test_name] = {
            'status': status,
            'details': details,
            'timestamp': time.time()
        }
        
    def log_error(self, error_msg):
        """记录错误"""
        self.errors.append(f"❌ {error_msg}")
        
    def log_warning(self, warning_msg):
        """记录警告"""
        self.warnings.append(f"⚠️ {warning_msg}")

    def test_rknn_detector_initialization(self):
        """测试RKNN检测器初始化"""
        print("🧪 测试1: RKNN检测器初始化")
        print("=" * 40)
        
        try:
            from garbage_sorter.detection.rknn_detector import RKNNDetector
            
            # 检查RKNN模型文件
            model_path = "detect/rknn-0804.rknn"
            if not os.path.exists(model_path):
                self.log_error(f"RKNN模型文件不存在: {model_path}")
                self.log_result("rknn_model_file", "FAIL", f"模型文件缺失: {model_path}")
                return False
            else:
                print(f"✅ RKNN模型文件存在: {model_path}")
                self.log_result("rknn_model_file", "PASS", f"模型文件: {model_path}")
            
            # 初始化检测器
            print("🔄 初始化RKNN检测器...")
            detector = RKNNDetector()
            
            # 检查NPU初始化
            if hasattr(detector, 'rknn_lite') and detector.rknn_lite is not None:
                print("✅ RKNN NPU初始化成功")
                self.log_result("rknn_npu_init", "PASS", "NPU初始化成功")
            else:
                self.log_error("RKNN NPU初始化失败")
                self.log_result("rknn_npu_init", "FAIL", "NPU初始化失败")
                return False
            
            # 检查ROI配置
            roi_areas = detector.get_roi_areas()
            if roi_areas and len(roi_areas) > 0:
                print(f"✅ ROI区域配置正确: {len(roi_areas)}个区域")
                for i, roi in enumerate(roi_areas):
                    print(f"   ROI {i+1}: {roi}")
                self.log_result("rknn_roi_config", "PASS", f"ROI配置: {len(roi_areas)}个区域")
            else:
                self.log_warning("ROI区域配置为空")
                self.log_result("rknn_roi_config", "WARN", "ROI配置为空")
            
            # 测试检测器启动
            if hasattr(detector, 'start_detection'):
                start_result = detector.start_detection()
                if start_result:
                    print("✅ RKNN检测器启动成功")
                    self.log_result("rknn_detector_start", "PASS", "检测器启动成功")
                    
                    # 停止检测器
                    if hasattr(detector, 'stop_detection'):
                        detector.stop_detection()
                        print("✅ RKNN检测器停止成功")
                else:
                    self.log_error("RKNN检测器启动失败")
                    self.log_result("rknn_detector_start", "FAIL", "检测器启动失败")
                    return False
            
            return True
            
        except ImportError as e:
            self.log_error(f"导入RKNN检测器失败: {e}")
            self.log_result("rknn_import", "FAIL", f"导入错误: {e}")
            return False
        except Exception as e:
            self.log_error(f"RKNN检测器初始化异常: {e}")
            self.log_result("rknn_init_exception", "FAIL", f"异常: {e}")
            return False

    def test_roi_coordinate_system(self):
        """测试ROI坐标系转换"""
        print("\n🧪 测试2: ROI坐标系转换")
        print("=" * 35)
        
        try:
            # 模拟坐标转换函数
            def convert_full_to_roi_coords(bbox, roi_area):
                if len(bbox) != 4:
                    return bbox
                roi_x1, roi_y1, roi_x2, roi_y2 = roi_area
                det_x1, det_y1, det_x2, det_y2 = bbox
                return [det_x1 - roi_x1, det_y1 - roi_y1, det_x2 - roi_x1, det_y2 - roi_y1]
            
            def is_bbox_in_roi(bbox, roi_area):
                if len(bbox) != 4:
                    return False
                roi_x1, roi_y1, roi_x2, roi_y2 = roi_area
                det_x1, det_y1, det_x2, det_y2 = bbox
                return not (det_x2 <= roi_x1 or det_x1 >= roi_x2 or 
                           det_y2 <= roi_y1 or det_y1 >= roi_y2)
            
            # 测试用例
            roi_area = (0, 120, 640, 360)
            test_cases = [
                {"name": "ROI内检测框", "bbox": [300, 200, 340, 240], "expected_in_roi": True},
                {"name": "ROI外检测框", "bbox": [300, 50, 340, 90], "expected_in_roi": False},
                {"name": "边界检测框", "bbox": [0, 120, 40, 160], "expected_in_roi": True},
                {"name": "部分重叠", "bbox": [600, 100, 650, 140], "expected_in_roi": True}
            ]
            
            all_passed = True
            for case in test_cases:
                bbox = case['bbox']
                expected = case['expected_in_roi']
                
                # 测试ROI检查
                in_roi = is_bbox_in_roi(bbox, roi_area)
                roi_coords = convert_full_to_roi_coords(bbox, roi_area)
                
                if in_roi == expected:
                    print(f"✅ {case['name']}: ROI检查正确 ({in_roi})")
                    print(f"   原始坐标: {bbox} → ROI坐标: {roi_coords}")
                else:
                    print(f"❌ {case['name']}: ROI检查错误 (期望: {expected}, 实际: {in_roi})")
                    all_passed = False
            
            if all_passed:
                self.log_result("roi_coordinate_conversion", "PASS", "所有坐标转换测试通过")
            else:
                self.log_result("roi_coordinate_conversion", "FAIL", "部分坐标转换测试失败")
            
            return all_passed
            
        except Exception as e:
            self.log_error(f"ROI坐标系测试异常: {e}")
            self.log_result("roi_coordinate_test", "FAIL", f"异常: {e}")
            return False

    def test_drop_detection_mechanism(self):
        """测试掉落检测触发机制"""
        print("\n🧪 测试3: 掉落检测触发机制")
        print("=" * 40)
        
        try:
            # 模拟GUI客户端状态
            class MockGUIClient:
                def __init__(self):
                    self.waiting_for_drop = True
                    self.drop_detection_enabled = False
                    self.detection_active = False
                    self.video_active = False
                    
                def start_system(self):
                    self.waiting_for_drop = True
                    self.drop_detection_enabled = True
                    self.detection_active = True
                    return "system_started"
                    
                def on_drop_detected(self):
                    if self.waiting_for_drop and self.drop_detection_enabled and self.detection_active:
                        self.waiting_for_drop = False
                        self.video_active = True
                        return "detection_started"
                    return "ignored"
                    
                def process_detection_frame(self):
                    if self.waiting_for_drop:
                        return "display_only"
                    return "detecting"
                    
                def reset_detection_state(self):
                    self.waiting_for_drop = True
                    self.video_active = False
                    return "reset_complete"
            
            # 测试流程
            client = MockGUIClient()
            
            # 1. 系统启动测试
            result = client.start_system()
            if client.waiting_for_drop and client.drop_detection_enabled:
                print("✅ 系统启动后正确进入等待掉落状态")
                self.log_result("drop_system_start", "PASS", "启动后等待掉落")
            else:
                print("❌ 系统启动状态错误")
                self.log_result("drop_system_start", "FAIL", "启动状态错误")
                return False
            
            # 2. 等待状态帧处理测试
            frame_result = client.process_detection_frame()
            if frame_result == "display_only":
                print("✅ 等待状态下正确只显示视频")
                self.log_result("drop_waiting_frame", "PASS", "等待状态帧处理正确")
            else:
                print("❌ 等待状态帧处理错误")
                self.log_result("drop_waiting_frame", "FAIL", "等待状态帧处理错误")
                return False
            
            # 3. 掉落检测触发测试
            drop_result = client.on_drop_detected()
            if drop_result == "detection_started" and not client.waiting_for_drop:
                print("✅ 掉落检测触发后正确启动检测")
                self.log_result("drop_trigger", "PASS", "掉落触发正确")
            else:
                print("❌ 掉落检测触发失败")
                self.log_result("drop_trigger", "FAIL", "掉落触发失败")
                return False
            
            # 4. 检测状态帧处理测试
            frame_result = client.process_detection_frame()
            if frame_result == "detecting":
                print("✅ 检测状态下正确进行检测")
                self.log_result("drop_detecting_frame", "PASS", "检测状态帧处理正确")
            else:
                print("❌ 检测状态帧处理错误")
                self.log_result("drop_detecting_frame", "FAIL", "检测状态帧处理错误")
                return False
            
            # 5. 状态重置测试
            reset_result = client.reset_detection_state()
            if client.waiting_for_drop and not client.video_active:
                print("✅ 状态重置后正确回到等待掉落状态")
                self.log_result("drop_state_reset", "PASS", "状态重置正确")
            else:
                print("❌ 状态重置错误")
                self.log_result("drop_state_reset", "FAIL", "状态重置错误")
                return False
            
            print("✅ 掉落检测触发机制测试全部通过")
            return True
            
        except Exception as e:
            self.log_error(f"掉落检测机制测试异常: {e}")
            self.log_result("drop_detection_test", "FAIL", f"异常: {e}")
            return False

    def test_gui_client_integration(self):
        """测试GUI客户端集成"""
        print("\n🧪 测试4: GUI客户端集成")
        print("=" * 35)
        
        try:
            from garbage_sorter.gui.gui_client import GarbageSorterGUI
            
            # 检查GUI客户端导入
            print("✅ GUI客户端导入成功")
            self.log_result("gui_import", "PASS", "GUI客户端导入成功")
            
            # 检查关键方法是否存在
            required_methods = [
                'start_system', 'stop_system', 'on_drop_detected',
                'process_detection_frame', 'reset_detection_state',
                'convert_full_to_roi_coords', 'is_bbox_in_roi',
                'draw_detection_results_for_roi', 'draw_roi_areas_for_roi'
            ]
            
            missing_methods = []
            for method in required_methods:
                if not hasattr(GarbageSorterGUI, method):
                    missing_methods.append(method)
            
            if missing_methods:
                self.log_error(f"GUI客户端缺少方法: {missing_methods}")
                self.log_result("gui_methods", "FAIL", f"缺少方法: {missing_methods}")
                return False
            else:
                print("✅ GUI客户端所有必需方法存在")
                self.log_result("gui_methods", "PASS", "所有必需方法存在")
            
            return True
            
        except ImportError as e:
            self.log_error(f"GUI客户端导入失败: {e}")
            self.log_result("gui_import", "FAIL", f"导入错误: {e}")
            return False
        except Exception as e:
            self.log_error(f"GUI客户端测试异常: {e}")
            self.log_result("gui_integration_test", "FAIL", f"异常: {e}")
            return False

    def test_esp32_communication(self):
        """测试ESP32通信"""
        print("\n🧪 测试5: ESP32通信")
        print("=" * 30)
        
        try:
            # 检查ESP32相关文件
            esp32_files = [
                "esp32/main/garbage_sorter.c",
                "esp32/main/CMakeLists.txt"
            ]
            
            for file_path in esp32_files:
                if os.path.exists(file_path):
                    print(f"✅ ESP32文件存在: {file_path}")
                else:
                    self.log_warning(f"ESP32文件不存在: {file_path}")
            
            # 检查通信协议
            expected_commands = ["1", "2", "3", "4", "TRUN", "TRUNB", "TS", "LIGHT_ON", "LIGHT_OFF"]
            print(f"✅ ESP32命令协议定义: {expected_commands}")
            self.log_result("esp32_commands", "PASS", f"命令协议: {expected_commands}")
            
            return True
            
        except Exception as e:
            self.log_error(f"ESP32通信测试异常: {e}")
            self.log_result("esp32_communication_test", "FAIL", f"异常: {e}")
            return False

    def generate_test_report(self):
        """生成测试报告"""
        print("\n" + "="*60)
        print("🎯 垃圾分拣系统全面验证测试报告")
        print("="*60)
        
        # 统计结果
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result['status'] == 'PASS')
        failed_tests = sum(1 for result in self.test_results.values() if result['status'] == 'FAIL')
        warning_tests = sum(1 for result in self.test_results.values() if result['status'] == 'WARN')
        
        print(f"\n📊 测试统计:")
        print(f"   总测试数: {total_tests}")
        print(f"   通过: {passed_tests} ✅")
        print(f"   失败: {failed_tests} ❌")
        print(f"   警告: {warning_tests} ⚠️")
        print(f"   成功率: {(passed_tests/total_tests*100):.1f}%")
        
        # 详细结果
        print(f"\n📋 详细测试结果:")
        for test_name, result in self.test_results.items():
            status_icon = {"PASS": "✅", "FAIL": "❌", "WARN": "⚠️"}.get(result['status'], "❓")
            print(f"   {status_icon} {test_name}: {result['status']}")
            if result['details']:
                print(f"      详情: {result['details']}")
        
        # 错误和警告
        if self.errors:
            print(f"\n❌ 发现的错误:")
            for error in self.errors:
                print(f"   {error}")
        
        if self.warnings:
            print(f"\n⚠️ 警告信息:")
            for warning in self.warnings:
                print(f"   {warning}")
        
        # 建议
        print(f"\n💡 建议:")
        if failed_tests == 0:
            print("   ✅ 所有核心功能测试通过，系统可以正常运行")
        else:
            print("   ❌ 存在失败的测试，建议修复后再次测试")
        
        if warning_tests > 0:
            print("   ⚠️ 存在警告项，建议检查相关配置")
        
        return {
            'total': total_tests,
            'passed': passed_tests,
            'failed': failed_tests,
            'warnings': warning_tests,
            'success_rate': passed_tests/total_tests*100 if total_tests > 0 else 0
        }

def main():
    """主测试函数"""
    print("🚀 启动垃圾分拣系统全面验证测试")
    print("="*50)
    
    # 创建测试框架
    test_framework = SystemTestFramework()
    
    # 执行测试
    tests = [
        test_framework.test_rknn_detector_initialization,
        test_framework.test_roi_coordinate_system,
        test_framework.test_drop_detection_mechanism,
        test_framework.test_gui_client_integration,
        test_framework.test_esp32_communication
    ]
    
    for test in tests:
        try:
            test()
        except Exception as e:
            test_framework.log_error(f"测试执行异常: {e}")
    
    # 生成报告
    report = test_framework.generate_test_report()
    
    return report

if __name__ == "__main__":
    main()
