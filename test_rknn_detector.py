#!/usr/bin/env python3
"""
测试RKNN检测器是否正常工作
"""

import cv2
import time
import sys
import os

# 添加项目路径
sys.path.append('src')

try:
    from garbage_sorter.detection.rknn_detector import RKNNDetector
    print("✅ RKNN检测器导入成功")
except ImportError as e:
    print(f"❌ RKNN检测器导入失败: {e}")
    sys.exit(1)

def test_rknn_detector():
    """测试RKNN检测器"""
    print("=== RKNN检测器测试 ===")
    
    # 初始化检测器
    detector = RKNNDetector(
        model_name='detect/rknn-0804.rknn',
        img_size=640,
        conf_thres=0.4
    )
    
    # 加载模型
    if not detector.load_model():
        print("❌ 模型加载失败")
        return False
    
    print("✅ 模型加载成功")
    
    # 启动检测
    if not detector.start_detection():
        print("❌ 检测启动失败")
        return False
    
    print("✅ 检测启动成功")
    
    # 打开摄像头
    cap = cv2.VideoCapture(0)
    if not cap.isOpened():
        print("❌ 摄像头打开失败")
        return False
    
    print("✅ 摄像头打开成功")
    print("按 'q' 退出测试")
    
    frame_count = 0
    detection_count = 0
    
    while True:
        ret, frame = cap.read()
        if not ret:
            print("❌ 读取帧失败")
            break
        
        frame_count += 1
        
        # 检测
        results = detector.detect_frame(frame)
        
        if results:
            detection_count += 1
            print(f"帧 {frame_count}: 检测到 {len(results)} 个对象")
            
            # 绘制检测结果
            for detection in results:
                bbox = detection.get('bbox', [])
                confidence = detection.get('confidence', 0.0)
                class_name = detection.get('class_name', 'unknown')
                
                if len(bbox) == 4:
                    x1, y1, x2, y2 = map(int, bbox)
                    
                    # 绘制边界框
                    cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
                    
                    # 绘制标签
                    label = f"{class_name}: {confidence:.2f}"
                    cv2.putText(frame, label, (x1, y1-10),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
        
        # 显示统计信息
        cv2.putText(frame, f"Frame: {frame_count}", (10, 30),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        cv2.putText(frame, f"Detections: {detection_count}", (10, 60),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        
        # 显示画面
        cv2.imshow('RKNN Detector Test', frame)
        
        # 检查退出
        if cv2.waitKey(1) & 0xFF == ord('q'):
            break
    
    # 清理
    cap.release()
    cv2.destroyAllWindows()
    detector.stop_detection()
    
    print(f"\n=== 测试结果 ===")
    print(f"总帧数: {frame_count}")
    print(f"检测次数: {detection_count}")
    print(f"检测率: {detection_count/frame_count*100:.1f}%" if frame_count > 0 else "N/A")
    
    return True

if __name__ == "__main__":
    test_rknn_detector()
