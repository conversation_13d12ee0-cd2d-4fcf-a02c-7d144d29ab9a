#!/usr/bin/env python3
"""
测试ROI裁剪逻辑 - 命令行版本
验证ROI区域裁剪的坐标计算
"""

import cv2
import numpy as np
import os

def test_roi_cropping():
    """测试ROI裁剪功能"""
    print("🧪 测试ROI区域裁剪逻辑")
    print("=" * 50)
    
    # ROI配置 - 与系统保持一致
    roi_area = (0, 120, 640, 360)  # 检测区域
    x1, y1, x2, y2 = roi_area
    
    print(f"ROI区域配置: {roi_area}")
    print(f"  X范围: {x1} -> {x2} (宽度: {x2-x1})")
    print(f"  Y范围: {y1} -> {y2} (高度: {y2-y1})")
    
    # 创建测试帧 640x480
    original_frame = np.zeros((480, 640, 3), dtype=np.uint8)
    
    # 绘制背景网格
    for x in range(0, 640, 50):
        cv2.line(original_frame, (x, 0), (x, 480), (50, 50, 50), 1)
    for y in range(0, 480, 50):
        cv2.line(original_frame, (0, y), (640, y), (50, 50, 50), 1)
    
    # 绘制ROI区域边界
    cv2.rectangle(original_frame, (x1, y1), (x2, y2), (0, 255, 0), 3)
    cv2.putText(original_frame, "ROI Detection Zone", (x1+10, y1-10),
               cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
    
    # 绘制分拣区域分界线
    cv2.line(original_frame, (213, y1), (213, y2), (255, 255, 0), 2)
    cv2.line(original_frame, (427, y1), (427, y2), (255, 255, 0), 2)
    
    # 标记区域外的内容（将被裁剪掉）
    cv2.putText(original_frame, "This area will be CROPPED", (10, 50),
               cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 255), 2)
    cv2.putText(original_frame, "This area will be CROPPED", (10, 450),
               cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 255), 2)
    
    # 在ROI区域内绘制内容
    cv2.putText(original_frame, "Left Zone", (50, y1+50),
               cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 0), 2)
    cv2.putText(original_frame, "Detection Zone", (250, y1+50),
               cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 0), 2)
    cv2.putText(original_frame, "Right Zone", (450, y1+50),
               cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 0), 2)
    
    # 绘制模拟垃圾对象
    # 有害垃圾 - 左分拣区
    cv2.rectangle(original_frame, (100, 200), (140, 230), (0, 0, 255), -1)
    cv2.putText(original_frame, "Harmful", (75, 190),
               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)
    
    # 可回收垃圾 - 右分拣区
    cv2.rectangle(original_frame, (500, 250), (540, 280), (0, 255, 0), -1)
    cv2.putText(original_frame, "Recyclable", (475, 240),
               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
    
    # 厨余垃圾 - 检测区
    cv2.rectangle(original_frame, (320, 220), (360, 250), (255, 0, 0), -1)
    cv2.putText(original_frame, "Kitchen", (295, 210),
               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 1)
    
    print(f"\n📊 原始帧信息:")
    print(f"  尺寸: {original_frame.shape[1]}x{original_frame.shape[0]}")
    print(f"  数据类型: {original_frame.dtype}")
    
    # 执行ROI裁剪
    print(f"\n✂️ 执行ROI裁剪...")
    cropped_frame = original_frame[y1:y2, x1:x2].copy()
    
    print(f"\n📊 裁剪后帧信息:")
    print(f"  尺寸: {cropped_frame.shape[1]}x{cropped_frame.shape[0]}")
    print(f"  数据类型: {cropped_frame.dtype}")
    print(f"  裁剪比例: {cropped_frame.shape[1]/original_frame.shape[1]:.2f} x {cropped_frame.shape[0]/original_frame.shape[0]:.2f}")
    
    # 在裁剪后的帧上添加信息
    cv2.putText(cropped_frame, f"ROI Cropped: {x2-x1}x{y2-y1}", (10, 30),
               cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
    cv2.putText(cropped_frame, "Only ROI content shown", (10, 60),
               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 1)
    
    # 保存测试图像
    output_dir = "test_output"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    original_path = os.path.join(output_dir, "original_frame.jpg")
    cropped_path = os.path.join(output_dir, "cropped_roi_frame.jpg")
    
    cv2.imwrite(original_path, original_frame)
    cv2.imwrite(cropped_path, cropped_frame)
    
    print(f"\n💾 测试图像已保存:")
    print(f"  原始帧: {original_path}")
    print(f"  ROI裁剪帧: {cropped_path}")
    
    # 测试检测框坐标转换
    print(f"\n🎯 测试检测框坐标转换:")
    test_detections = [
        {'bbox': [100, 200, 140, 230], 'class_name': 'harmful', 'confidence': 0.85},
        {'bbox': [320, 220, 360, 250], 'class_name': 'kitchen', 'confidence': 0.92},
        {'bbox': [500, 250, 540, 280], 'class_name': 'recyclable', 'confidence': 0.78}
    ]
    
    for i, detection in enumerate(test_detections):
        bbox = detection['bbox']
        class_name = detection['class_name']
        confidence = detection['confidence']
        
        # 原始坐标
        x1_orig, y1_orig, x2_orig, y2_orig = bbox
        center_x_orig = (x1_orig + x2_orig) // 2
        center_y_orig = (y1_orig + y2_orig) // 2
        
        # ROI裁剪后的坐标（Y坐标需要减去ROI的Y偏移）
        y1_roi = y1_orig - y1  # 减去ROI的Y偏移
        y2_roi = y2_orig - y1
        center_y_roi = (y1_roi + y2_roi) // 2
        
        # 判断位置
        if center_x_orig <= 213:
            position = "左分拣区"
        elif center_x_orig >= 427:
            position = "右分拣区"
        else:
            position = "检测区"
        
        print(f"  检测{i+1}: {class_name}")
        print(f"    原始坐标: ({x1_orig},{y1_orig}) -> ({x2_orig},{y2_orig})")
        print(f"    ROI坐标: ({x1_orig},{y1_roi}) -> ({x2_orig},{y2_roi})")
        print(f"    中心点: 原始({center_x_orig},{center_y_orig}) -> ROI({center_x_orig},{center_y_roi})")
        print(f"    位置: {position}")
        print(f"    置信度: {confidence:.2f}")
        print()
    
    print("✅ ROI裁剪测试完成！")
    print("\n📝 总结:")
    print("  1. ROI区域成功裁剪，只保留感兴趣区域")
    print("  2. 检测框X坐标保持不变，Y坐标需要减去ROI偏移")
    print("  3. 位置判断逻辑基于X坐标，不受ROI裁剪影响")
    print("  4. 界面显示将只显示ROI区域内容，区域外内容被裁剪")

def test_coordinate_mapping():
    """测试坐标映射逻辑"""
    print(f"\n🧪 测试坐标映射逻辑")
    print("=" * 30)
    
    roi_y_offset = 120  # ROI的Y偏移
    
    test_cases = [
        # (原始Y坐标, 期望的ROI Y坐标)
        (120, 0),    # ROI顶部
        (240, 120),  # ROI中间
        (360, 240),  # ROI底部
        (200, 80),   # 任意位置
        (300, 180),  # 任意位置
    ]
    
    for orig_y, expected_roi_y in test_cases:
        roi_y = orig_y - roi_y_offset
        status = "✅" if roi_y == expected_roi_y else "❌"
        print(f"  {status} 原始Y={orig_y} -> ROI Y={roi_y} (期望={expected_roi_y})")

if __name__ == "__main__":
    try:
        test_roi_cropping()
        test_coordinate_mapping()
        
    except Exception as e:
        print(f"\n❌ 测试出错: {e}")
        import traceback
        traceback.print_exc()
