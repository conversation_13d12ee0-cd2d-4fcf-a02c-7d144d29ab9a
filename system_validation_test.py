#!/usr/bin/env python3
"""
垃圾分拣系统实际验证测试
"""

import os
import sys
import time
import traceback
from pathlib import Path

def test_file_structure():
    """测试文件结构完整性"""
    print("🧪 测试1: 文件结构完整性")
    print("=" * 35)
    
    required_files = [
        "src/garbage_sorter/gui/gui_client.py",
        "src/garbage_sorter/detection/rknn_detector.py",
        "detect/rknn-0804.rknn",
        "esp32/main/garbage_sorter.c"
    ]
    
    missing_files = []
    existing_files = []
    
    for file_path in required_files:
        if os.path.exists(file_path):
            existing_files.append(file_path)
            print(f"✅ {file_path}")
        else:
            missing_files.append(file_path)
            print(f"❌ {file_path}")
    
    print(f"\n📊 文件检查结果:")
    print(f"   存在: {len(existing_files)}/{len(required_files)}")
    print(f"   缺失: {len(missing_files)}")
    
    if missing_files:
        print(f"   缺失文件: {missing_files}")
        return False
    
    return True

def test_imports():
    """测试关键模块导入"""
    print("\n🧪 测试2: 关键模块导入")
    print("=" * 30)
    
    # 添加项目路径
    sys.path.insert(0, str(Path(__file__).parent / "src"))
    
    import_tests = [
        ("cv2", "OpenCV"),
        ("numpy", "NumPy"),
        ("tkinter", "Tkinter"),
        ("PIL", "Pillow"),
        ("serial", "PySerial")
    ]
    
    successful_imports = 0
    failed_imports = []
    
    for module_name, display_name in import_tests:
        try:
            __import__(module_name)
            print(f"✅ {display_name} ({module_name})")
            successful_imports += 1
        except ImportError as e:
            print(f"❌ {display_name} ({module_name}): {e}")
            failed_imports.append((module_name, str(e)))
    
    print(f"\n📊 导入测试结果:")
    print(f"   成功: {successful_imports}/{len(import_tests)}")
    print(f"   失败: {len(failed_imports)}")
    
    if failed_imports:
        print("   失败模块:")
        for module, error in failed_imports:
            print(f"     {module}: {error}")
    
    return len(failed_imports) == 0

def test_gui_client_modifications():
    """测试GUI客户端修改"""
    print("\n🧪 测试3: GUI客户端修改验证")
    print("=" * 40)
    
    try:
        # 读取GUI客户端文件
        gui_file = "src/garbage_sorter/gui/gui_client.py"
        if not os.path.exists(gui_file):
            print(f"❌ GUI客户端文件不存在: {gui_file}")
            return False
        
        with open(gui_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查修复1: 掉落检测触发机制
        drop_detection_checks = [
            "waiting_for_drop",
            "drop_detection_enabled",
            "on_drop_detected",
            "等待垃圾投放检测触发"
        ]
        
        drop_detection_found = 0
        for check in drop_detection_checks:
            if check in content:
                print(f"✅ 掉落检测机制: {check}")
                drop_detection_found += 1
            else:
                print(f"❌ 掉落检测机制缺失: {check}")
        
        # 检查修复2: ROI坐标系转换
        roi_conversion_checks = [
            "convert_full_to_roi_coords",
            "is_bbox_in_roi",
            "draw_detection_results_for_roi",
            "ROI相对坐标"
        ]
        
        roi_conversion_found = 0
        for check in roi_conversion_checks:
            if check in content:
                print(f"✅ ROI坐标转换: {check}")
                roi_conversion_found += 1
            else:
                print(f"❌ ROI坐标转换缺失: {check}")
        
        print(f"\n📊 GUI客户端修改验证:")
        print(f"   掉落检测机制: {drop_detection_found}/{len(drop_detection_checks)}")
        print(f"   ROI坐标转换: {roi_conversion_found}/{len(roi_conversion_checks)}")
        
        total_checks = len(drop_detection_checks) + len(roi_conversion_checks)
        total_found = drop_detection_found + roi_conversion_found
        success_rate = (total_found / total_checks) * 100
        
        print(f"   总体完成度: {total_found}/{total_checks} ({success_rate:.1f}%)")
        
        return success_rate >= 80  # 80%以上认为成功
        
    except Exception as e:
        print(f"❌ GUI客户端测试异常: {e}")
        return False

def test_coordinate_conversion_logic():
    """测试坐标转换逻辑"""
    print("\n🧪 测试4: 坐标转换逻辑")
    print("=" * 35)
    
    def convert_full_to_roi_coords(bbox, roi_area):
        """坐标转换函数"""
        if len(bbox) != 4:
            return bbox
        roi_x1, roi_y1, roi_x2, roi_y2 = roi_area
        det_x1, det_y1, det_x2, det_y2 = bbox
        return [det_x1 - roi_x1, det_y1 - roi_y1, det_x2 - roi_x1, det_y2 - roi_y1]
    
    def is_bbox_in_roi(bbox, roi_area):
        """ROI范围检查"""
        if len(bbox) != 4:
            return False
        roi_x1, roi_y1, roi_x2, roi_y2 = roi_area
        det_x1, det_y1, det_x2, det_y2 = bbox
        return not (det_x2 <= roi_x1 or det_x1 >= roi_x2 or 
                   det_y2 <= roi_y1 or det_y1 >= roi_y2)
    
    # 测试用例
    roi_area = (0, 120, 640, 360)  # 实际ROI区域
    test_cases = [
        {
            "name": "中心区域检测框",
            "bbox": [300, 200, 340, 240],
            "expected_in_roi": True,
            "expected_roi_coords": [300, 80, 340, 120]
        },
        {
            "name": "左上角检测框",
            "bbox": [50, 150, 90, 190],
            "expected_in_roi": True,
            "expected_roi_coords": [50, 30, 90, 70]
        },
        {
            "name": "右下角检测框",
            "bbox": [550, 300, 590, 340],
            "expected_in_roi": True,
            "expected_roi_coords": [550, 180, 590, 220]
        },
        {
            "name": "ROI外检测框",
            "bbox": [300, 50, 340, 90],
            "expected_in_roi": False,
            "expected_roi_coords": [300, -70, 340, -30]
        }
    ]
    
    passed_tests = 0
    total_tests = len(test_cases)
    
    print(f"ROI区域: {roi_area}")
    print()
    
    for i, case in enumerate(test_cases, 1):
        bbox = case['bbox']
        expected_in_roi = case['expected_in_roi']
        expected_roi_coords = case['expected_roi_coords']
        
        # 执行测试
        actual_in_roi = is_bbox_in_roi(bbox, roi_area)
        actual_roi_coords = convert_full_to_roi_coords(bbox, roi_area)
        
        # 检查结果
        roi_check_pass = actual_in_roi == expected_in_roi
        coord_check_pass = actual_roi_coords == expected_roi_coords
        
        print(f"测试 {i}: {case['name']}")
        print(f"   原始坐标: {bbox}")
        print(f"   ROI检查: {actual_in_roi} (期望: {expected_in_roi}) {'✅' if roi_check_pass else '❌'}")
        print(f"   坐标转换: {actual_roi_coords} (期望: {expected_roi_coords}) {'✅' if coord_check_pass else '❌'}")
        
        if roi_check_pass and coord_check_pass:
            passed_tests += 1
            print(f"   结果: ✅ 通过")
        else:
            print(f"   结果: ❌ 失败")
        print()
    
    print(f"📊 坐标转换测试结果:")
    print(f"   通过: {passed_tests}/{total_tests}")
    print(f"   成功率: {(passed_tests/total_tests)*100:.1f}%")
    
    return passed_tests == total_tests

def test_rknn_model_file():
    """测试RKNN模型文件"""
    print("\n🧪 测试5: RKNN模型文件")
    print("=" * 30)
    
    model_path = "detect/rknn-0804.rknn"
    
    if not os.path.exists(model_path):
        print(f"❌ RKNN模型文件不存在: {model_path}")
        return False
    
    # 检查文件大小
    file_size = os.path.getsize(model_path)
    file_size_mb = file_size / (1024 * 1024)
    
    print(f"✅ RKNN模型文件存在: {model_path}")
    print(f"✅ 文件大小: {file_size_mb:.2f} MB")
    
    # 检查文件是否为空
    if file_size == 0:
        print(f"❌ RKNN模型文件为空")
        return False
    
    # 检查文件扩展名
    if not model_path.endswith('.rknn'):
        print(f"❌ RKNN模型文件扩展名错误")
        return False
    
    print(f"✅ RKNN模型文件验证通过")
    return True

def generate_validation_report():
    """生成验证报告"""
    print("\n" + "="*60)
    print("🎯 垃圾分拣系统验证报告")
    print("="*60)
    
    # 执行所有测试
    tests = [
        ("文件结构完整性", test_file_structure),
        ("关键模块导入", test_imports),
        ("GUI客户端修改", test_gui_client_modifications),
        ("坐标转换逻辑", test_coordinate_conversion_logic),
        ("RKNN模型文件", test_rknn_model_file)
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            traceback.print_exc()
            results[test_name] = False
    
    # 统计结果
    total_tests = len(results)
    passed_tests = sum(1 for result in results.values() if result)
    failed_tests = total_tests - passed_tests
    success_rate = (passed_tests / total_tests) * 100
    
    print(f"\n📊 验证统计:")
    print(f"   总测试数: {total_tests}")
    print(f"   通过: {passed_tests} ✅")
    print(f"   失败: {failed_tests} ❌")
    print(f"   成功率: {success_rate:.1f}%")
    
    print(f"\n📋 详细结果:")
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    # 系统状态评估
    print(f"\n🎯 系统状态评估:")
    if success_rate >= 90:
        print("   ✅ 系统状态优秀，可以正常运行")
    elif success_rate >= 70:
        print("   ⚠️ 系统状态良好，建议修复失败项")
    else:
        print("   ❌ 系统存在重要问题，需要修复后再运行")
    
    # 具体建议
    print(f"\n💡 建议:")
    if not results.get("文件结构完整性", False):
        print("   📁 检查必需文件是否存在")
    if not results.get("关键模块导入", False):
        print("   📦 安装缺失的Python包")
    if not results.get("GUI客户端修改", False):
        print("   🔧 检查GUI客户端修改是否正确应用")
    if not results.get("坐标转换逻辑", False):
        print("   📐 验证坐标转换函数实现")
    if not results.get("RKNN模型文件", False):
        print("   🤖 检查RKNN模型文件")
    
    return {
        'total': total_tests,
        'passed': passed_tests,
        'failed': failed_tests,
        'success_rate': success_rate,
        'results': results
    }

def main():
    """主函数"""
    print("🚀 启动垃圾分拣系统验证测试")
    print("="*50)
    
    report = generate_validation_report()
    
    return report

if __name__ == "__main__":
    main()
