#!/usr/bin/env python3
"""
RKNN检测器性能测试
对比优化前后的性能差异
"""

import time
import cv2
import numpy as np
from src.garbage_sorter.detection.rknn_detector import RKNNDetector
import logging

# 设置日志级别
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_test_frame():
    """创建测试帧"""
    # 创建一个640x480的测试图像
    frame = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
    
    # 添加一些简单的形状来模拟垃圾对象
    cv2.rectangle(frame, (100, 100), (200, 200), (255, 0, 0), -1)  # 蓝色矩形
    cv2.circle(frame, (400, 300), 50, (0, 255, 0), -1)  # 绿色圆形
    cv2.rectangle(frame, (300, 150), (450, 250), (0, 0, 255), -1)  # 红色矩形
    
    return frame

def test_detection_performance():
    """测试检测性能"""
    print("🚀 RKNN检测器性能测试")
    print("=" * 50)
    
    # 初始化检测器
    detector = RKNNDetector(
        model_name="rknn-0804",
        img_size=640,
        conf_thres=0.4
    )
    
    # 加载模型
    if not detector.load_model():
        print("❌ 模型加载失败，使用模拟模式进行测试")
    else:
        print("✅ 模型加载成功")
    
    # 启动检测
    detector.start_detection()
    
    # 创建测试帧
    test_frame = create_test_frame()
    
    # 性能测试参数
    num_frames = 100
    warmup_frames = 10
    
    print(f"\n📊 开始性能测试 ({num_frames} 帧)")
    print("-" * 30)
    
    # 预热阶段
    print("🔥 预热阶段...")
    for i in range(warmup_frames):
        detector.detect_frame(test_frame)
        time.sleep(0.01)  # 模拟实际帧间隔
    
    # 重置统计
    detector.reset_stats()
    
    # 正式测试
    print("⚡ 正式测试...")
    start_time = time.time()
    
    for i in range(num_frames):
        frame_start = time.time()
        
        # 执行检测
        results = detector.detect_frame(test_frame)
        
        frame_time = time.time() - frame_start
        
        # 每20帧打印一次进度
        if (i + 1) % 20 == 0:
            print(f"  处理帧 {i+1}/{num_frames}, 当前帧耗时: {frame_time*1000:.1f}ms")
        
        # 模拟实际应用的帧间隔
        time.sleep(0.005)  # 5ms间隔，模拟200FPS的理论上限
    
    total_time = time.time() - start_time
    
    # 获取性能统计
    perf_stats = detector.get_performance_stats()
    detection_stats = detector.get_detection_stats()
    
    # 停止检测
    detector.stop_detection()
    
    # 输出结果
    print("\n📈 性能测试结果")
    print("=" * 50)
    print(f"总测试时间: {total_time:.2f}s")
    print(f"平均FPS: {num_frames/total_time:.1f}")
    print(f"平均帧处理时间: {(total_time/num_frames)*1000:.1f}ms")
    
    print(f"\n📊 帧处理统计:")
    print(f"  提交帧数: {perf_stats['submitted']}")
    print(f"  处理帧数: {perf_stats['processed']}")
    print(f"  检测到目标帧数: {perf_stats['detected']}")
    print(f"  丢弃帧数: {perf_stats['dropped']}")
    print(f"  检测率: {perf_stats['detection_rate']*100:.1f}%")
    print(f"  丢帧率: {perf_stats['drop_rate']*100:.1f}%")
    print(f"  处理率: {perf_stats['process_rate']*100:.1f}%")
    
    print(f"\n🎯 检测统计:")
    print(f"  总检测次数: {detection_stats['total_detections']}")
    print(f"  成功检测次数: {detection_stats['successful_detections']}")
    print(f"  失败检测次数: {detection_stats['failed_detections']}")
    print(f"  平均置信度: {detection_stats['average_confidence']:.3f}")
    
    # 性能评估
    print(f"\n🏆 性能评估:")
    avg_fps = num_frames/total_time
    if avg_fps >= 30:
        print(f"  ✅ 优秀 - FPS: {avg_fps:.1f} (≥30)")
    elif avg_fps >= 20:
        print(f"  ✅ 良好 - FPS: {avg_fps:.1f} (≥20)")
    elif avg_fps >= 10:
        print(f"  ⚠️  一般 - FPS: {avg_fps:.1f} (≥10)")
    else:
        print(f"  ❌ 需要优化 - FPS: {avg_fps:.1f} (<10)")
    
    if perf_stats['drop_rate'] < 0.05:
        print(f"  ✅ 丢帧率优秀: {perf_stats['drop_rate']*100:.1f}% (<5%)")
    elif perf_stats['drop_rate'] < 0.1:
        print(f"  ⚠️  丢帧率一般: {perf_stats['drop_rate']*100:.1f}% (<10%)")
    else:
        print(f"  ❌ 丢帧率过高: {perf_stats['drop_rate']*100:.1f}% (≥10%)")

def test_continuous_detection():
    """测试连续检测模式"""
    print("\n🔄 连续检测模式测试")
    print("=" * 50)
    
    detector = RKNNDetector(
        model_name="rknn-0804",
        img_size=640,
        conf_thres=0.4
    )
    
    detector.load_model()
    detector.start_detection()
    
    test_frame = create_test_frame()
    
    print("开始连续检测 (10秒)...")
    start_time = time.time()
    frame_count = 0
    
    while time.time() - start_time < 10:  # 运行10秒
        results = detector.detect_frame(test_frame)
        frame_count += 1
        
        # 每秒打印一次统计
        if frame_count % 100 == 0:
            elapsed = time.time() - start_time
            current_fps = frame_count / elapsed
            perf_stats = detector.get_performance_stats()
            print(f"  {elapsed:.1f}s - FPS: {current_fps:.1f}, "
                  f"检测率: {perf_stats['detection_rate']*100:.1f}%, "
                  f"丢帧率: {perf_stats['drop_rate']*100:.1f}%")
        
        time.sleep(0.01)  # 100FPS理论上限
    
    total_time = time.time() - start_time
    final_stats = detector.get_performance_stats()
    
    detector.stop_detection()
    
    print(f"\n连续检测结果:")
    print(f"  总时间: {total_time:.1f}s")
    print(f"  总帧数: {frame_count}")
    print(f"  平均FPS: {frame_count/total_time:.1f}")
    print(f"  最终检测率: {final_stats['detection_rate']*100:.1f}%")
    print(f"  最终丢帧率: {final_stats['drop_rate']*100:.1f}%")

if __name__ == "__main__":
    try:
        test_detection_performance()
        test_continuous_detection()
        print("\n🎉 性能测试完成！")
        
    except KeyboardInterrupt:
        print("\n⏹️  测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试出错: {e}")
        import traceback
        traceback.print_exc()
