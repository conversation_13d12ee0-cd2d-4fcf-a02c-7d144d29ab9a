#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
垃圾分拣系统 - 主包
基于ESP32和OrangePi5的智能垃圾分拣系统
"""

__version__ = "1.0.0"
__author__ = "Garbage Sorter Team"
__description__ = "基于ESP32和OrangePi5的智能垃圾分拣系统"

# 导出主要类
from .hardware.esp32_controller import ESP32Controller
from .hardware.orangepi_client import GarbageSorterClient
from .detection.rknn_detector import RKNNDetector
from .gui.gui_client import GarbageSorterGUI
from .gui.esp32_test_interface import ESP32TestInterface

# 导入工具模块
from .utils.config import load_config, save_config

__all__ = [
    # 硬件控制
    'ESP32Controller',
    'GarbageSorterClient',

    # 检测模块
    'RKNNDetector',

    # GUI模块
    'GarbageSorterGUI',
    'ESP32TestInterface',

    # 工具模块
    'load_config',
    'save_config',
]
