#!/usr/bin/env python3
"""
测试修复后的ROI显示功能
验证ROI区域内容完整显示
"""

import cv2
import numpy as np
import os
import sys

# 添加项目路径
sys.path.append('src')

def create_test_frame():
    """创建测试帧"""
    # 创建640x480的测试帧
    frame = np.zeros((480, 640, 3), dtype=np.uint8)
    
    # 绘制背景纹理
    for y in range(0, 480, 20):
        for x in range(0, 640, 20):
            if (x//20 + y//20) % 2 == 0:
                cv2.rectangle(frame, (x, y), (x+20, y+20), (40, 40, 40), -1)
            else:
                cv2.rectangle(frame, (x, y), (x+20, y+20), (60, 60, 60), -1)
    
    # 绘制ROI区域边界 (0, 120, 640, 360)
    roi_area = (0, 120, 640, 360)
    x1, y1, x2, y2 = roi_area
    cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 3)
    cv2.putText(frame, "ROI Detection Zone", (x1+10, y1-10),
               cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
    
    # 在ROI区域内绘制分拣区域分界线
    cv2.line(frame, (213, y1), (213, y2), (255, 255, 0), 2)
    cv2.line(frame, (427, y1), (427, y2), (255, 255, 0), 2)
    
    # 标记分拣区域
    cv2.putText(frame, "Left Zone", (50, y1+40),
               cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 0), 2)
    cv2.putText(frame, "(Harmful/Other)", (30, y1+70),
               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 0), 1)
    
    cv2.putText(frame, "Detection Zone", (250, y1+40),
               cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 0), 2)
    cv2.putText(frame, "(Moving Area)", (250, y1+70),
               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 0), 1)
    
    cv2.putText(frame, "Right Zone", (450, y1+40),
               cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 0), 2)
    cv2.putText(frame, "(Recyclable/Kitchen)", (430, y1+70),
               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 0), 1)
    
    # 绘制模拟垃圾对象
    # 有害垃圾 - 左分拣区
    cv2.rectangle(frame, (100, 200), (140, 230), (0, 0, 255), -1)
    cv2.putText(frame, "Harmful", (75, 190),
               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 2)
    cv2.putText(frame, "0.85", (105, 250),
               cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 0, 255), 1)
    
    # 可回收垃圾 - 右分拣区
    cv2.rectangle(frame, (500, 250), (540, 280), (0, 255, 0), -1)
    cv2.putText(frame, "Recyclable", (475, 240),
               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
    cv2.putText(frame, "0.92", (505, 300),
               cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 0), 1)
    
    # 厨余垃圾 - 检测区
    cv2.rectangle(frame, (320, 220), (360, 250), (255, 0, 0), -1)
    cv2.putText(frame, "Kitchen", (295, 210),
               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 2)
    cv2.putText(frame, "0.78", (325, 270),
               cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 0, 0), 1)
    
    # 在ROI区域外绘制内容（将被裁剪掉）
    cv2.putText(frame, "This content will be CROPPED", (10, 50),
               cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 255), 2)
    cv2.putText(frame, "This content will be CROPPED", (10, 450),
               cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 255), 2)
    
    # 绘制传送带
    cv2.rectangle(frame, (0, y1+100), (640, y1+120), (139, 69, 19), -1)
    cv2.putText(frame, "Conveyor Belt", (250, y1+115),
               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
    
    return frame

def simulate_roi_display_logic(frame):
    """模拟ROI显示逻辑"""
    print("🎬 模拟ROI显示逻辑")
    print("=" * 40)
    
    # 1. 调整尺寸到配置的视频尺寸
    target_width = 640
    target_height = 480
    frame = cv2.resize(frame, (target_width, target_height))
    print(f"1. 调整尺寸: {target_width}x{target_height}")
    
    # 2. 绘制检测结果（模拟）
    print("2. 绘制检测结果...")
    # 这里模拟检测结果已经绘制在帧上
    
    # 3. 绘制ROI区域标识（模拟）
    print("3. 绘制ROI区域标识...")
    # 这里模拟ROI标识已经绘制在帧上
    
    # 4. 获取ROI区域配置并裁剪
    roi_area = (0, 120, 640, 360)  # 检测区域
    x1, y1, x2, y2 = roi_area
    print(f"4. ROI区域配置: {roi_area}")
    
    # 确保坐标在帧范围内
    x1 = max(0, min(x1, target_width))
    y1 = max(0, min(y1, target_height))
    x2 = max(x1, min(x2, target_width))
    y2 = max(y1, min(y2, target_height))
    print(f"5. 坐标范围检查: ({x1},{y1}) -> ({x2},{y2})")
    
    # 裁剪到ROI区域
    cropped_frame = frame[y1:y2, x1:x2]
    print(f"6. 裁剪完成: 尺寸 {x2-x1}x{y2-y1}")
    
    return frame, cropped_frame

def test_roi_display():
    """测试ROI显示功能"""
    print("🧪 测试修复后的ROI显示功能")
    print("=" * 50)
    
    # 创建测试帧
    print("📷 创建测试帧...")
    original_frame = create_test_frame()
    
    # 模拟ROI显示逻辑
    full_frame, roi_frame = simulate_roi_display_logic(original_frame.copy())
    
    # 保存测试结果
    output_dir = "test_output"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 保存原始帧
    original_path = os.path.join(output_dir, "test_original_with_content.jpg")
    cv2.imwrite(original_path, original_frame)
    
    # 保存完整处理后的帧
    full_path = os.path.join(output_dir, "test_full_processed.jpg")
    cv2.imwrite(full_path, full_frame)
    
    # 保存ROI裁剪后的帧
    roi_path = os.path.join(output_dir, "test_roi_cropped_with_content.jpg")
    cv2.imwrite(roi_path, roi_frame)
    
    print(f"\n💾 测试结果已保存:")
    print(f"  原始帧: {original_path}")
    print(f"  处理后完整帧: {full_path}")
    print(f"  ROI裁剪帧: {roi_path}")
    
    # 分析结果
    print(f"\n📊 结果分析:")
    print(f"  原始帧尺寸: {original_frame.shape[1]}x{original_frame.shape[0]}")
    print(f"  ROI帧尺寸: {roi_frame.shape[1]}x{roi_frame.shape[0]}")
    print(f"  裁剪比例: {roi_frame.shape[1]/original_frame.shape[1]:.2f} x {roi_frame.shape[0]/original_frame.shape[0]:.2f}")
    
    # 检查ROI帧内容
    print(f"\n🔍 ROI帧内容检查:")
    
    # 检查是否有非零像素（即有内容）
    non_zero_pixels = np.count_nonzero(roi_frame)
    total_pixels = roi_frame.shape[0] * roi_frame.shape[1] * roi_frame.shape[2]
    content_ratio = non_zero_pixels / total_pixels
    
    print(f"  非零像素数: {non_zero_pixels:,}")
    print(f"  总像素数: {total_pixels:,}")
    print(f"  内容比例: {content_ratio:.2%}")
    
    if content_ratio > 0.1:  # 如果有超过10%的像素有内容
        print("  ✅ ROI区域包含丰富内容")
    else:
        print("  ❌ ROI区域内容过少，可能存在问题")
    
    # 检查是否包含预期的颜色
    print(f"\n🎨 颜色内容检查:")
    
    # 检查是否有红色（有害垃圾）
    red_mask = (roi_frame[:,:,2] > 200) & (roi_frame[:,:,1] < 50) & (roi_frame[:,:,0] < 50)
    red_pixels = np.sum(red_mask)
    print(f"  红色像素（有害垃圾）: {red_pixels}")
    
    # 检查是否有绿色（可回收垃圾）
    green_mask = (roi_frame[:,:,1] > 200) & (roi_frame[:,:,2] < 50) & (roi_frame[:,:,0] < 50)
    green_pixels = np.sum(green_mask)
    print(f"  绿色像素（可回收垃圾）: {green_pixels}")
    
    # 检查是否有蓝色（厨余垃圾）
    blue_mask = (roi_frame[:,:,0] > 200) & (roi_frame[:,:,1] < 50) & (roi_frame[:,:,2] < 50)
    blue_pixels = np.sum(blue_mask)
    print(f"  蓝色像素（厨余垃圾）: {blue_pixels}")
    
    # 检查是否有黄色（分界线）
    yellow_mask = (roi_frame[:,:,1] > 200) & (roi_frame[:,:,2] > 200) & (roi_frame[:,:,0] < 50)
    yellow_pixels = np.sum(yellow_mask)
    print(f"  黄色像素（分界线）: {yellow_pixels}")
    
    if red_pixels > 0 and green_pixels > 0 and blue_pixels > 0 and yellow_pixels > 0:
        print("  ✅ ROI区域包含所有预期的颜色内容")
        print("  ✅ 检测框、分界线、标签都正确显示")
    else:
        print("  ⚠️  某些颜色内容可能缺失")
    
    print(f"\n🎉 ROI显示功能测试完成！")
    print("现在ROI区域应该包含完整的检测内容，而不是只有两条线。")

if __name__ == "__main__":
    try:
        test_roi_display()
        
    except Exception as e:
        print(f"\n❌ 测试出错: {e}")
        import traceback
        traceback.print_exc()
