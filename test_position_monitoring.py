#!/usr/bin/env python3
"""
测试位置监控的分拣流程
验证检测到harmful后的完整处理逻辑
"""

import time
import numpy as np
from src.garbage_sorter.detection.rknn_detector import RKNNDetector
import logging

# 设置日志级别
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def simulate_harmful_detection():
    """模拟检测到有害垃圾的完整流程"""
    print("🧪 测试位置监控的分拣流程")
    print("=" * 50)
    
    # 初始化检测器
    detector = RKNNDetector(
        model_name="rknn-0804",
        img_size=640,
        conf_thres=0.4
    )
    
    # 加载模型
    if not detector.load_model():
        print("❌ 模型加载失败，使用模拟模式进行测试")
    else:
        print("✅ 模型加载成功")
    
    # 启动检测
    detector.start_detection()
    
    # 创建测试帧
    test_frame = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
    
    print("\n📊 模拟检测流程")
    print("-" * 30)
    
    # 模拟检测到有害垃圾
    print("🔍 步骤1: 检测阶段")
    results = detector.detect_frame(test_frame)

    if results:
        print(f"✅ 检测到 {len(results)} 个对象")
        for i, result in enumerate(results):
            class_name = result.get('class_name', 'unknown')
            confidence = result.get('confidence', 0.0)
            bbox = result.get('bbox', [])
            print(f"  对象{i+1}: {class_name}, 置信度: {confidence:.2f}, 位置: {bbox}")

            # 如果检测到有害垃圾
            if class_name == 'harmful':
                print(f"\n🎯 检测到有害垃圾！开始处理流程...")
                simulate_sorting_process(result)
                return

    # 如果没有检测到结果，手动创建一个有害垃圾检测结果进行测试
    print("❌ 未检测到任何对象，创建模拟有害垃圾检测结果...")
    mock_result = {
        'class_name': 'harmful',
        'confidence': 0.85,
        'bbox': [300, 200, 400, 300],  # 在检测区的位置
        'class_id': 0
    }
    print(f"🎯 模拟检测到有害垃圾！开始处理流程...")
    simulate_sorting_process(mock_result)
    
    # 停止检测
    detector.stop_detection()
    print("\n🎉 测试完成！")

def simulate_sorting_process(detection_result):
    """模拟分拣处理流程"""
    class_name = detection_result.get('class_name')
    confidence = detection_result.get('confidence', 0.0)
    bbox = detection_result.get('bbox', [])
    
    print(f"\n📋 分拣流程开始")
    print(f"  垃圾类型: {class_name}")
    print(f"  置信度: {confidence:.2f}")
    print(f"  检测框: {bbox}")
    
    # 步骤1: 类型映射
    print(f"\n🏷️ 步骤2: 类型映射")
    display_name = "有害垃圾"
    garbage_id = 2
    print(f"  显示名称: {display_name}")
    print(f"  分拣ID: {garbage_id}")
    
    # 步骤2: 确定目标区域
    print(f"\n📍 步骤3: 确定目标区域")
    target_zone = "左分拣区"
    belt_command = "TRUNB"  # 倒转
    print(f"  目标区域: {target_zone}")
    print(f"  传送带命令: {belt_command}")
    
    # 步骤3: 获取当前位置
    print(f"\n📍 步骤4: 获取当前位置")
    current_position = simulate_get_position(bbox)
    print(f"  当前位置: {current_position}")
    
    # 步骤4: 传送带控制
    print(f"\n🚛 步骤5: 传送带控制")
    if current_position != target_zone:
        print(f"  发送传送带命令: {belt_command}")
        print(f"  传送带启动成功")
        
        # 步骤5: 位置监控
        print(f"\n👁️ 步骤6: 开始位置监控")
        simulate_position_monitoring(current_position, target_zone, garbage_id, display_name)
    else:
        print(f"  垃圾已在目标区域，直接分拣")
        simulate_immediate_sorting(garbage_id, display_name)

def simulate_get_position(bbox):
    """模拟获取垃圾位置"""
    if len(bbox) != 4:
        return "未知位置"
    
    x1, _, x2, _ = bbox
    center_x = (x1 + x2) // 2
    
    # 根据位置判断当前在哪个区域 (640宽度)
    if center_x <= 213:  # 左分拣区域 (0-213)
        return "左分拣区"
    elif center_x >= 427:  # 右分拣区域 (427-640)
        return "右分拣区"
    else:  # 中间区域 (214-426)
        return "检测区"

def simulate_position_monitoring(start_position, target_zone, garbage_id, display_name):
    """模拟位置监控过程"""
    print(f"  开始监控: {start_position} -> {target_zone}")
    
    # 模拟垃圾移动过程
    positions = ["检测区", "左分拣区"] if target_zone == "左分拣区" else ["检测区", "右分拣区"]
    
    for pos in positions:
        time.sleep(0.5)  # 模拟移动时间
        print(f"  监控中: 当前位置 = {pos}")
        
        if pos == target_zone:
            print(f"  ✅ 垃圾已到达目标区域: {target_zone}")
            simulate_immediate_sorting(garbage_id, display_name)
            return
    
    print(f"  ❌ 监控超时")

def simulate_immediate_sorting(garbage_id, display_name):
    """模拟立即分拣"""
    print(f"\n🛑 步骤7: 停止传送带")
    print(f"  发送停止命令: TS")
    print(f"  传送带已停止")
    
    print(f"\n⚙️ 步骤8: 执行分拣动作")
    print(f"  发送分拣命令: {garbage_id}")
    print(f"  ESP32响应: OK")
    
    print(f"\n🤖 步骤9: ESP32硬件执行")
    print(f"  识别为: GARBAGE_HARMFUL")
    print(f"  执行动作: move_brush_m3(true) - M3前进推有害垃圾")
    print(f"  自动返回中间位置")
    
    print(f"\n📊 步骤10: 更新统计")
    print(f"  有害垃圾计数 +1")
    
    print(f"\n✅ 步骤11: 任务完成")
    print(f"  等待ESP32完成分拣动作...")
    time.sleep(1)  # 模拟等待
    print(f"  分拣任务完成: {display_name}")
    print(f"  重置系统状态，准备下一次检测")
    print(f"  === 分拣任务完全完成，系统准备就绪 ===")

def test_position_detection():
    """测试位置检测逻辑"""
    print("\n🧪 测试位置检测逻辑")
    print("=" * 30)
    
    test_cases = [
        ([100, 100, 200, 200], "左分拣区"),    # center_x = 150
        ([300, 100, 400, 200], "检测区"),      # center_x = 350  
        ([500, 100, 600, 200], "右分拣区"),    # center_x = 550
        ([0, 100, 100, 200], "左分拣区"),      # center_x = 50
        ([540, 100, 640, 200], "右分拣区"),    # center_x = 590
    ]
    
    for bbox, expected in test_cases:
        result = simulate_get_position(bbox)
        status = "✅" if result == expected else "❌"
        center_x = (bbox[0] + bbox[2]) // 2
        print(f"  {status} bbox={bbox}, center_x={center_x}, 预期={expected}, 实际={result}")

if __name__ == "__main__":
    try:
        simulate_harmful_detection()
        test_position_detection()
        
    except KeyboardInterrupt:
        print("\n⏹️  测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试出错: {e}")
        import traceback
        traceback.print_exc()
