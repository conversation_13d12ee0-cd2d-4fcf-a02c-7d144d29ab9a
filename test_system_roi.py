#!/usr/bin/env python3
"""
测试垃圾分拣系统的ROI显示功能
验证实际系统中的ROI裁剪是否正常工作
"""

import sys
import os
import time
import cv2
import numpy as np

# 添加项目路径
sys.path.append('src')

def test_system_roi():
    """测试系统ROI功能"""
    print("🧪 测试垃圾分拣系统ROI显示功能")
    print("=" * 50)
    
    try:
        # 导入系统组件
        from garbage_sorter.detection.rknn_detector import RKNNDetector
        from garbage_sorter.utils.config import load_config
        
        print("✅ 成功导入系统组件")
        
        # 初始化配置
        config = load_config()
        print("✅ 配置管理器初始化成功")
        
        # 初始化RKNN检测器
        detector = RKNNDetector(config)
        print("✅ RKNN检测器初始化成功")
        
        # 获取ROI配置
        roi_areas = detector.get_roi_areas()
        print(f"📍 ROI区域配置:")
        for i, roi in enumerate(roi_areas):
            print(f"  区域{i+1}: {roi['name']}")
            print(f"    坐标: {roi['area']}")
            print(f"    用途: {roi.get('purpose', 'unknown')}")
            print(f"    颜色: {roi['color']}")
        
        # 找到检测ROI
        detection_roi = None
        for roi in roi_areas:
            if roi.get('purpose') == 'detection':
                detection_roi = roi['area']
                break
        
        if detection_roi:
            x1, y1, x2, y2 = detection_roi
            print(f"\n🎯 检测ROI区域: ({x1},{y1}) -> ({x2},{y2})")
            print(f"   宽度: {x2-x1}px")
            print(f"   高度: {y2-y1}px")
            print(f"   面积: {(x2-x1)*(y2-y1):,}px²")
        else:
            print("❌ 未找到检测ROI区域")
            return
        
        # 创建模拟帧来测试ROI裁剪
        print(f"\n🎬 创建模拟帧测试ROI裁剪...")
        
        # 创建640x480的测试帧
        frame = np.zeros((480, 640, 3), dtype=np.uint8)
        
        # 绘制背景网格
        for x in range(0, 640, 50):
            cv2.line(frame, (x, 0), (x, 480), (50, 50, 50), 1)
        for y in range(0, 480, 50):
            cv2.line(frame, (0, y), (640, y), (50, 50, 50), 1)
        
        # 标记ROI区域外的内容
        cv2.putText(frame, "OUTSIDE ROI - WILL BE CROPPED", (10, 50),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 255), 2)
        cv2.putText(frame, "OUTSIDE ROI - WILL BE CROPPED", (10, 450),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 255), 2)
        
        # 绘制ROI区域边界
        cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 3)
        cv2.putText(frame, "ROI Detection Zone", (x1+10, y1-10),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
        
        # 在ROI区域内绘制内容
        cv2.putText(frame, "INSIDE ROI - WILL BE SHOWN", (x1+50, y1+50),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        
        # 绘制分拣区域分界线
        cv2.line(frame, (213, y1), (213, y2), (255, 255, 0), 2)
        cv2.line(frame, (427, y1), (427, y2), (255, 255, 0), 2)
        
        # 标记分拣区域
        cv2.putText(frame, "Left Zone", (50, y1+100),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)
        cv2.putText(frame, "Detection", (250, y1+100),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)
        cv2.putText(frame, "Right Zone", (450, y1+100),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)
        
        # 绘制模拟垃圾
        cv2.rectangle(frame, (100, 200), (140, 230), (0, 0, 255), -1)
        cv2.putText(frame, "Harmful", (75, 190),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 2)
        
        cv2.rectangle(frame, (320, 220), (360, 250), (255, 0, 0), -1)
        cv2.putText(frame, "Kitchen", (295, 210),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 2)
        
        cv2.rectangle(frame, (500, 250), (540, 280), (0, 255, 0), -1)
        cv2.putText(frame, "Recyclable", (475, 240),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
        
        print(f"✅ 模拟帧创建完成: {frame.shape[1]}x{frame.shape[0]}")
        
        # 模拟系统的ROI裁剪逻辑
        print(f"\n✂️ 执行ROI裁剪...")
        
        # 调整尺寸到配置的视频尺寸（使用默认值）
        target_width = config.get('video', {}).get('width', 640)
        target_height = config.get('video', {}).get('height', 480)
        frame_resized = cv2.resize(frame, (target_width, target_height))
        print(f"1. 调整尺寸: {target_width}x{target_height}")
        
        # 确保坐标在帧范围内
        x1_safe = max(0, min(x1, target_width))
        y1_safe = max(0, min(y1, target_height))
        x2_safe = max(x1_safe, min(x2, target_width))
        y2_safe = max(y1_safe, min(y2, target_height))
        print(f"2. 安全坐标: ({x1_safe},{y1_safe}) -> ({x2_safe},{y2_safe})")
        
        # 裁剪到ROI区域
        roi_frame = frame_resized[y1_safe:y2_safe, x1_safe:x2_safe]
        print(f"3. 裁剪完成: {roi_frame.shape[1]}x{roi_frame.shape[0]}")
        
        # 保存测试结果
        output_dir = "test_output"
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        original_path = os.path.join(output_dir, "system_test_original.jpg")
        resized_path = os.path.join(output_dir, "system_test_resized.jpg")
        roi_path = os.path.join(output_dir, "system_test_roi.jpg")
        
        cv2.imwrite(original_path, frame)
        cv2.imwrite(resized_path, frame_resized)
        cv2.imwrite(roi_path, roi_frame)
        
        print(f"\n💾 测试结果已保存:")
        print(f"  原始帧: {original_path}")
        print(f"  调整尺寸后: {resized_path}")
        print(f"  ROI裁剪后: {roi_path}")
        
        # 分析ROI内容
        print(f"\n📊 ROI内容分析:")
        non_zero = np.count_nonzero(roi_frame)
        total = roi_frame.shape[0] * roi_frame.shape[1] * roi_frame.shape[2]
        content_ratio = non_zero / total
        
        print(f"  非零像素: {non_zero:,} / {total:,}")
        print(f"  内容比例: {content_ratio:.2%}")
        
        if content_ratio > 0.5:
            print("  ✅ ROI区域包含丰富内容")
        else:
            print("  ⚠️  ROI区域内容较少")
        
        # 检查关键颜色
        print(f"\n🎨 关键内容检查:")
        
        # 检查绿色边界线
        green_mask = (roi_frame[:,:,1] > 200) & (roi_frame[:,:,0] < 100) & (roi_frame[:,:,2] < 100)
        green_pixels = np.sum(green_mask)
        print(f"  绿色边界线: {green_pixels} 像素")
        
        # 检查黄色分界线
        yellow_mask = (roi_frame[:,:,1] > 200) & (roi_frame[:,:,2] > 200) & (roi_frame[:,:,0] < 100)
        yellow_pixels = np.sum(yellow_mask)
        print(f"  黄色分界线: {yellow_pixels} 像素")
        
        # 检查垃圾对象
        red_mask = (roi_frame[:,:,2] > 200) & (roi_frame[:,:,1] < 100) & (roi_frame[:,:,0] < 100)
        red_pixels = np.sum(red_mask)
        print(f"  红色垃圾对象: {red_pixels} 像素")
        
        if green_pixels > 0 and yellow_pixels > 0 and red_pixels > 0:
            print("  ✅ 所有关键内容都正确显示在ROI中")
        else:
            print("  ⚠️  某些关键内容可能缺失")
        
        print(f"\n🎉 系统ROI测试完成！")
        print("现在系统应该正确显示ROI区域内的完整内容。")
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保在正确的环境中运行此测试")
    except Exception as e:
        print(f"❌ 测试出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_system_roi()
