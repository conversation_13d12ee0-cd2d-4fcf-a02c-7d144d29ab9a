#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OrangePi客户端
负责与ESP32通信和视频处理
"""

import cv2
import time
import threading
import logging
from typing import Dict, Any, Optional, Callable
from .esp32_controller import ESP32<PERSON>ontroller
try:
    import serial
    import serial.tools.list_ports
    SERIAL_AVAILABLE = True
except ImportError:
    # 如果没有安装pyserial，使用模拟版本
    SERIAL_AVAILABLE = False
    serial = None

logger = logging.getLogger(__name__)

class VideoInterface:
    """视频接口类"""
    
    def __init__(self):
        self.cap = None
        self.is_playing = False
        self.frame_callback = None
        self.video_thread = None
        
    def start_camera(self, camera_id: int = 0) -> bool:
        """启动摄像头"""
        try:
            # 尝试多种摄像头打开方式
            camera_attempts = [
                (camera_id, None),  # 默认方式
                (camera_id, cv2.CAP_DSHOW),  # Windows DirectShow
                (camera_id, cv2.CAP_V4L2),  # Linux V4L2
                (-1, None),  # 自动检测
            ]

            self.cap = None
            for cam_id, backend in camera_attempts:
                try:
                    if backend is not None:
                        self.cap = cv2.VideoCapture(cam_id, backend)
                    else:
                        self.cap = cv2.VideoCapture(cam_id)

                    if self.cap.isOpened():
                        # 测试是否能读取帧
                        ret, _ = self.cap.read()
                        if ret:
                            logger.info(f"摄像头连接成功: ID={cam_id}, Backend={backend}")
                            break
                        else:
                            self.cap.release()
                            self.cap = None
                    else:
                        if self.cap:
                            self.cap.release()
                        self.cap = None
                except Exception as e:
                    logger.debug(f"摄像头尝试失败 ID={cam_id}, Backend={backend}: {e}")
                    if self.cap:
                        self.cap.release()
                    self.cap = None

            if not self.cap or not self.cap.isOpened():
                logger.error("无法打开摄像头")
                return False

            # 设置摄像头参数 - 640x640与RKNN检测输入尺寸一致
            self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
            self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 640)  # 与RKNN输入尺寸一致
            self.cap.set(cv2.CAP_PROP_FPS, 60)  # 提高帧率
            self.cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)  # 最小缓冲
            # 禁用自动曝光以减少处理时间
            self.cap.set(cv2.CAP_PROP_AUTO_EXPOSURE, 0.25)
            # 设置固定曝光值
            self.cap.set(cv2.CAP_PROP_EXPOSURE, -6)

            self.is_playing = True
            self.video_thread = threading.Thread(target=self._video_loop, daemon=True)
            self.video_thread.start()

            logger.info("摄像头启动成功")
            return True

        except Exception as e:
            logger.error(f"摄像头启动失败: {e}")
            # 回退到模拟模式
            logger.info("回退到视频模拟模式")
            self.cap = None
            self.is_playing = True
            self.video_thread = threading.Thread(target=self._simulation_video_loop, daemon=True)
            self.video_thread.start()
            return True
    
    def stop_camera(self):
        """停止摄像头"""
        self.is_playing = False
        
        if self.video_thread and self.video_thread.is_alive():
            self.video_thread.join(timeout=1.0)
        
        if self.cap:
            self.cap.release()
            self.cap = None
            
        logger.info("摄像头已停止")
    
    def set_frame_callback(self, callback: Callable):
        """设置帧回调函数"""
        self.frame_callback = callback
    
    def _video_loop(self):
        """视频循环 - 优化实时性"""
        while self.is_playing and self.cap:
            try:
                # 使用grab/retrieve分离以减少延迟
                if self.cap.grab():
                    ret, frame = self.cap.retrieve()
                    if ret and self.frame_callback:
                        self.frame_callback(frame)

                time.sleep(0.016)  # 60 FPS - 提高响应速度

            except Exception as e:
                logger.error(f"视频处理错误: {e}")
                break


    
    def get_frame(self):
        """获取单帧"""
        if self.cap and self.cap.isOpened():
            ret, frame = self.cap.read()
            if ret:
                return frame
        return None

class GarbageSorterClient:
    """垃圾分拣系统客户端"""
    
    def __init__(self, port: str = None, baudrate: int = 115200):
        # 智能默认端口选择
        if port is None:
            import platform
            system = platform.system()
            if system == "Windows":
                default_port = "COM3"  # Windows 常见端口
            elif system == "Linux":
                default_port = "/dev/ttyACM0"  # Linux ESP32 常见端口
            else:
                default_port = "/dev/ttyACM0"  # 其他系统默认
        else:
            default_port = port
            
        self.esp32_controller = ESP32Controller(default_port, baudrate)
        self.video_interface = VideoInterface()
        self.callbacks = {}
        self.is_connected = False
        
        # 自动检测端口
        if port is None:
            detected_port = self.auto_detect_port()
            if detected_port:
                self.esp32_controller.port = detected_port
    
    def auto_detect_port(self) -> Optional[str]:
        """自动检测ESP32端口（支持COM和TTY）"""
        try:
            if not SERIAL_AVAILABLE:
                logger.warning("pyserial未安装，无法自动检测端口")
                return None

            ports = list(serial.tools.list_ports.comports())
            logger.info(f"扫描到 {len(ports)} 个串口设备")

            # ESP32设备识别模式
            esp32_patterns = [
                'CH340', 'CH341',           # 常见的USB转串口芯片
                'CP210', 'CP2102', 'CP2104', # Silicon Labs芯片
                'FT232', 'FTDI',            # FTDI芯片
                'USB-SERIAL', 'USB Serial', # 通用USB串口
                'ESP32', 'ESP-32',          # ESP32设备
                'Silicon Labs',             # Silicon Labs设备
                'QinHeng',                  # 沁恒芯片
            ]

            for port in ports:
                port_info = f"{port.device} - {port.description}"
                logger.info(f"检查串口: {port_info}")

                # 检查设备描述是否包含ESP32相关关键词
                description_lower = port.description.lower()
                for pattern in esp32_patterns:
                    if pattern.lower() in description_lower:
                        logger.info(f"找到可能的ESP32设备: {port.device} ({port.description})")

                        # 测试连接
                        if self._test_esp32_connection(port.device):
                            logger.info(f"确认ESP32设备: {port.device}")
                            return port.device

            # 如果没有找到匹配的设备，返回第一个可用串口
            if ports:
                first_port = ports[0].device
                logger.info(f"未找到ESP32设备，尝试使用第一个串口: {first_port}")
                return first_port

            logger.warning("未检测到任何串口设备")
            return None

        except Exception as e:
            logger.error(f"端口检测失败: {e}")
            return None

    def _test_esp32_connection(self, port: str) -> bool:
        """测试ESP32连接"""
        try:
            if not SERIAL_AVAILABLE:
                return False

            # 尝试打开串口
            test_serial = serial.Serial(
                port=port,
                baudrate=115200,
                timeout=1.0
            )

            # 发送测试命令
            test_serial.write(b"STATUS\n")
            time.sleep(0.1)

            # 读取响应
            response = test_serial.read(100)
            test_serial.close()

            # 检查是否有响应
            if response:
                logger.debug(f"端口 {port} 测试成功，响应: {response}")
                return True
            else:
                logger.debug(f"端口 {port} 无响应")
                return False

        except Exception as e:
            logger.debug(f"端口 {port} 测试失败: {e}")
            return False
    
    def _test_esp32_connection(self, port: str) -> bool:
        """测试ESP32连接"""
        try:
            test_serial = serial.Serial(port, 115200, timeout=1)
            time.sleep(0.5)
            
            # 发送STATUS命令测试
            test_serial.write(b"STATUS\n")
            test_serial.flush()
            
            # 等待响应
            time.sleep(0.5)
            
            test_serial.close()
            return True  # 简化测试，只要能打开端口就认为成功
            
        except Exception:
            return False
    
    def connect(self) -> bool:
        """连接ESP32设备"""
        try:
            # 首先尝试连接真实设备
            if self.esp32_controller.connect():
                self.is_connected = True
                self._register_esp32_callbacks()
                logger.info("客户端连接成功")
                return True
            else:
                logger.warning("真实设备连接失败，使用模拟模式...")
                # 直接使用模拟模式
                if self.esp32_controller.connect():  # ESP32Controller会自动切换到模拟模式
                    self.is_connected = True
                    self._register_esp32_callbacks()
                    logger.info("模拟模式连接成功")
                    return True
                else:
                    logger.error("模拟模式连接失败")
                    return False

        except Exception as e:
            logger.error(f"连接错误: {e}")
            logger.info("使用模拟模式...")
            # 直接使用模拟模式
            if self.esp32_controller.connect():  # ESP32Controller会自动切换到模拟模式
                self.is_connected = True
                self._register_esp32_callbacks()
                logger.info("模拟模式连接成功")
                return True
            else:
                logger.error("模拟模式连接失败")
                return False
    
    def disconnect(self):
        """断开连接"""
        self.is_connected = False
        self.esp32_controller.disconnect()
        self.video_interface.stop_camera()
        logger.info("客户端已断开连接")

    def check_for_signals(self):
        """检查传感器信号（兼容SensorManager接口）"""
        # 返回传感器状态字典
        return {
            'start_button': False,  # GPIO41按键通过回调处理
            'drop_detected': False,  # 掉落检测通过回调处理
            'full_detected': False,  # 满载检测通过回调处理
        }

    def _register_esp32_callbacks(self):
        """注册ESP32回调"""
        self.esp32_controller.register_callback('start_button', self._on_start_button)
        self.esp32_controller.register_callback('drop_detected', self._on_drop_detected)
        self.esp32_controller.register_callback('full_detected', self._on_full_detected)
        self.esp32_controller.register_callback('status_update', self._on_status_update)
    
    def register_callback(self, event_type: str, callback: Callable):
        """注册客户端回调"""
        if event_type not in self.callbacks:
            self.callbacks[event_type] = []
        self.callbacks[event_type].append(callback)
    
    def _trigger_callback(self, event_type: str, *args, **kwargs):
        """触发回调"""
        if event_type in self.callbacks:
            for callback in self.callbacks[event_type]:
                try:
                    callback(*args, **kwargs)
                except Exception as e:
                    logger.error(f"回调执行错误 {event_type}: {e}")
    
    # ESP32事件处理
    def _on_start_button(self):
        """启动按钮事件"""
        logger.info("接收到GPIO41按键事件")
        self._trigger_callback('start_button')
    
    def _on_drop_detected(self):
        """掉落检测事件"""
        self._trigger_callback('drop_detected')
    
    def _on_full_detected(self):
        """满载检测事件"""
        self._trigger_callback('full_detected')
    
    def _on_status_update(self, status: str):
        """状态更新事件"""
        self._trigger_callback('status_update', status)
    
    # 硬件控制方法
    def start_conveyor(self) -> bool:
        """启动传送带"""
        return self.esp32_controller.start_conveyor()
    
    def stop_conveyor(self) -> bool:
        """停止传送带"""
        return self.esp32_controller.stop_conveyor()
    
    def reverse_conveyor(self) -> bool:
        """反转传送带"""
        return self.esp32_controller.reverse_conveyor()
    
    def turn_on_light(self) -> bool:
        """开启补光灯"""
        return self.esp32_controller.turn_on_light()
    
    def turn_off_light(self) -> bool:
        """关闭补光灯"""
        return self.esp32_controller.turn_off_light()
    
    def control_m1_motor(self, enable: bool) -> bool:
        """控制M1电机"""
        return self.esp32_controller.control_m1_motor(enable)
    
    def sort_garbage(self, category: int) -> bool:
        """垃圾分类"""
        return self.esp32_controller.sort_garbage(category)
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        return self.esp32_controller.get_system_status()

    def send_command(self, command: str) -> Optional[str]:
        """发送命令到ESP32"""
        return self.esp32_controller.send_command(command)

    # 视频相关方法
    def start_camera(self, camera_id: int = 0) -> bool:
        """启动摄像头"""
        return self.video_interface.start_camera(camera_id)
    
    def stop_camera(self):
        """停止摄像头"""
        self.video_interface.stop_camera()
    
    def set_frame_callback(self, callback: Callable):
        """设置帧回调"""
        self.video_interface.set_frame_callback(callback)
    
    def get_frame(self):
        """获取帧"""
        return self.video_interface.get_frame()
