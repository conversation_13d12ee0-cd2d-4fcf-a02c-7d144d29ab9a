#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ESP32控制器
负责与ESP32设备的串口通信和硬件控制
"""

try:
    import serial
    SERIAL_AVAILABLE = True
except ImportError:
    # 如果没有安装pyserial，使用模拟模式
    SERIAL_AVAILABLE = False
    serial = None
import time
import threading
import logging
from typing import Dict, Any, Callable, Optional

logger = logging.getLogger(__name__)

class ESP32Controller:
    """ESP32硬件控制器"""

    def __init__(self, port: str = '/dev/ttyACM0', baudrate: int = 115200):
        self.port = port
        self.baudrate = baudrate
        self.serial_port = None
        self.is_connected_flag = False
        self.read_thread = None
        self.running = False
        self.callbacks = {}
        self.simulation_mode = False

        # 消息缓冲区
        self.message_buffer = ""
        self.response_buffer = ""
        self.waiting_for_response = False
        self.expected_command = ""
        
    def connect(self) -> bool:
        """连接ESP32设备"""
        try:
            if self.is_connected_flag:
                self.disconnect()

            logger.info(f"正在连接ESP32设备: {self.port}")

            # 检查是否有真实串口
            if not SERIAL_AVAILABLE or not self._try_real_serial():
                # 使用模拟模式
                self.simulation_mode = True
                self.is_connected_flag = True
                logger.info("使用ESP32模拟模式")
                self._start_simulation_thread()
                return True

            return True

        except Exception as e:
            logger.error(f"ESP32连接失败: {e}")
            # 回退到模拟模式
            self.simulation_mode = True
            self.is_connected_flag = True
            logger.info("连接失败，使用ESP32模拟模式")
            self._start_simulation_thread()
            return True

    def _try_real_serial(self) -> bool:
        """尝试真实串口连接"""
        try:
            self.serial_port = serial.Serial(
                port=self.port,
                baudrate=self.baudrate,
                bytesize=serial.EIGHTBITS,
                parity=serial.PARITY_NONE,
                stopbits=serial.STOPBITS_ONE,
                timeout=1.0
            )
            logger.info(f"真实串口连接成功: {self.port}")

            time.sleep(0.5)  # 等待连接稳定

            # 清空缓冲区
            self.serial_port.flushInput()
            self.serial_port.flushOutput()

            self.is_connected_flag = True
            self.simulation_mode = False
            self.running = True
            
            # 启动读取线程
            self.start_reading_thread()
            
            logger.info(f"ESP32连接成功: {self.port}")
            return True
            
        except Exception as e:
            logger.error(f"ESP32连接失败: {e}")
            self.is_connected_flag = False
            return False

    def _start_simulation_thread(self):
        """启动模拟线程"""
        self.running = True
        self.read_thread = threading.Thread(target=self._simulation_loop, daemon=True)
        self.read_thread.start()
        logger.info("ESP32模拟线程已启动")

    def _simulation_loop(self):
        """模拟循环 - 简单模拟ESP32响应"""
        logger.info("ESP32模拟循环开始")
        while self.running and self.is_connected_flag:
            try:
                # 模拟定期发送状态信息
                time.sleep(2.0)
                if self.running:
                    # 模拟状态响应
                    self._handle_simulation_message("STATUS:READY")

                # 不再发送随机事件，只通过手动模拟按钮触发
                # 所有事件都通过GUI上的模拟按钮手动触发

            except Exception as e:
                logger.error(f"模拟循环错误: {e}")
                time.sleep(1.0)

        logger.info("ESP32模拟循环结束")

    def _handle_simulation_message(self, message: str):
        """处理模拟消息"""
        try:
            # 触发相应的回调
            if "STATUS" in message:
                self._trigger_callback('status_update', message)
            elif "DROP_DETECTED" in message:
                self._trigger_callback('drop_detected')
            elif "FULL_DETECTED" in message:
                self._trigger_callback('full_detected')
            elif "BUTTON_PRESSED" in message:
                self._trigger_callback('start_button')

        except Exception as e:
            logger.error(f"处理模拟消息失败: {e}")

    def disconnect(self):
        """断开ESP32连接"""
        self.running = False
        self.is_connected_flag = False
        
        if self.read_thread and self.read_thread.is_alive():
            self.read_thread.join(timeout=1.0)
        
        if self.serial_port and self.serial_port.is_open:
            self.serial_port.close()
            
        logger.info("ESP32连接已断开")
    
    def is_connected(self) -> bool:
        """检查连接状态"""
        if self.simulation_mode:
            return self.is_connected_flag
        return self.is_connected_flag and self.serial_port and self.serial_port.is_open
    
    def start_reading_thread(self):
        """启动读取线程"""
        if self.read_thread is None or not self.read_thread.is_alive():
            self.read_thread = threading.Thread(target=self._read_loop, daemon=True)
            self.read_thread.start()
            logger.info("ESP32读取线程已启动")
    
    def _read_loop(self):
        """读取循环"""
        logger.info("ESP32读取循环开始")
        while self.running and self.is_connected():
            try:
                if self.serial_port.in_waiting > 0:
                    data = self.serial_port.read(self.serial_port.in_waiting)
                    if data:
                        logger.debug(f"读取到数据: {data}")
                        self._process_received_data(data.decode('utf-8', errors='ignore'))

                time.sleep(0.01)  # 10ms间隔

            except Exception as e:
                logger.error(f"读取数据错误: {e}")
                break
        logger.info("ESP32读取循环结束")
    
    def _process_received_data(self, data: str):
        """处理接收到的数据"""
        self.message_buffer += data
        
        # 处理完整的消息（以换行符分隔）
        while '\n' in self.message_buffer:
            line, self.message_buffer = self.message_buffer.split('\n', 1)
            line = line.strip()
            
            if line:
                self._handle_message(line)
    
    def _handle_message(self, message: str):
        """处理接收到的消息"""
        # 优化日志：过滤冗余的ESP32调试信息
        if self._should_log_message(message):
            logger.info(f"收到ESP32消息: {message}")

        # 解析不同类型的消息
        if message == "GPIO41_BUTTON_PRESSED":
            logger.info("触发GPIO41按键回调")
            self._trigger_callback('start_button')
        elif message == "DROP_DETECTED":
            self._trigger_callback('drop_detected')
        elif message == "M1_DROP_DETECTED":
            self._trigger_callback('m1_drop_detected')
        elif message == "FULL_DETECTED":
            self._trigger_callback('full_detected')
        elif message == "SYSTEM_READY":
            logger.info("ESP32系统就绪")
            self._trigger_callback('system_ready')
        elif message.startswith("STATUS:"):
            self._trigger_callback('status_update', message)
        elif (message.endswith("_OK") or message.endswith("_FAIL") or message == "OK" or
              message == "RECEIVED" or "ERROR" in message or
              "TRAPEZOIDAL_RUN_START" in message or "TRAPEZOIDAL_RUN_BACKWARD_START" in message or
              "TRAPEZOIDAL_STOP" in message or "M1_ALREADY_RUNNING" in message or
              "M1_NOT_RUNNING" in message or "M1_RESTART_ERROR" in message or
              "LIGHT_TOGGLE_OK" in message):
            # 命令响应消息
            if self.waiting_for_response:
                self.response_buffer = message
            self._trigger_callback('command_response', message)
    
    def register_callback(self, event_type: str, callback: Callable):
        """注册回调函数"""
        if event_type not in self.callbacks:
            self.callbacks[event_type] = []
        self.callbacks[event_type].append(callback)
        logger.debug(f"注册回调: {event_type}")
    
    def _trigger_callback(self, event_type: str, *args, **kwargs):
        """触发回调函数"""
        if event_type in self.callbacks:
            for callback in self.callbacks[event_type]:
                try:
                    callback(*args, **kwargs)
                except Exception as e:
                    logger.error(f"回调执行错误 {event_type}: {e}")

    def _should_log_message(self, message: str) -> bool:
        """判断是否应该记录此消息到日志"""
        # 过滤掉冗余的ESP32调试信息
        filter_patterns = [
            "接收到",  # 接收到 X 字节USB数据
            "Process完整Command:",  # Process完整Command: XXX
            "=== ProcessCommand:",  # === ProcessCommand: [XXX] 长度: X ===
            "Decelerating: step=",  # 减速过程中的步数信息
            "加速中:",  # 加速过程中的步数信息
            "Task: conveyor=",  # 传送带任务状态信息
            "M1运行状态: 步数=",  # M1运行状态信息
            "CommandProcessComplete",  # 命令处理完成
            "Start deceleration:",  # 开始减速
            "Start平滑减速Stop",  # 开始平滑减速
            "简单梯形加速",  # 梯形加速相关
            "起始速度:",  # 速度参数信息
        ]

        # 检查是否包含需要过滤的模式
        for pattern in filter_patterns:
            if pattern in message:
                return False

        # 保留重要的消息
        important_patterns = [
            "RECEIVED",  # 命令确认
            "OK",  # 执行完成
            "ERROR",  # 错误信息
            "TRAPEZOIDAL_",  # 传送带状态变化
            "LIGHT_",  # 补光灯状态
            "M1_",  # M1电机状态
            "DROP_DETECTED",  # 掉落检测
            "FULL_DETECTED",  # 满载检测
            "GPIO41_BUTTON_PRESSED",  # 按钮事件
            "防卡",  # 防卡相关
            "刷板.*限位",  # 刷板到达限位
            "刷板.*Complete",  # 刷板操作完成
            "垃圾ProcessComplete",  # 垃圾处理完成
            "超时",  # 超时事件
            "🔴",  # 重要事件标记
            "🟢",  # 重要事件标记
            "⚠️",  # 警告标记
        ]

        # 检查是否包含重要信息
        for pattern in important_patterns:
            if pattern in message:
                return True

        # 默认不记录其他消息
        return False

    def _wait_for_response(self, command: str, timeout: float = 5.0) -> str:
        """等待ESP32响应"""
        self.waiting_for_response = True
        self.expected_command = command
        self.response_buffer = ""

        start_time = time.time()

        while time.time() - start_time < timeout:
            if self.response_buffer:
                # 检查是否收到了预期的响应
                response = self.response_buffer.strip()

                # 垃圾分拣命令(1-4)的响应检查
                if command in ['1', '2', '3', '4']:
                    if "RECEIVED" in response:
                        # 收到确认，清空缓冲区继续等待OK
                        logger.debug(f"收到命令确认: {response}")
                        self.response_buffer = ""
                        continue
                    elif "OK" in response:
                        logger.debug(f"垃圾分拣命令完成: {response}")
                        self.waiting_for_response = False
                        return response

                # 传送带命令的响应检查
                elif command == "TRUN" and "TRAPEZOIDAL_RUN_START" in response:
                    logger.debug(f"传送带启动确认: {response}")
                    self.waiting_for_response = False
                    return response
                elif command == "TRUNB" and "TRAPEZOIDAL_RUN_BACKWARD_START" in response:
                    logger.debug(f"传送带反转确认: {response}")
                    self.waiting_for_response = False
                    return response
                elif command == "TS" and "TRAPEZOIDAL_STOP" in response:
                    logger.debug(f"传送带停止确认: {response}")
                    self.waiting_for_response = False
                    return response

                # 补光灯命令的响应检查
                elif command == "LIGHT_ON" and "LIGHT_ON_OK" in response:
                    logger.debug(f"补光灯开启确认: {response}")
                    self.waiting_for_response = False
                    return response
                elif command == "LIGHT_OFF" and "LIGHT_OFF_OK" in response:
                    logger.debug(f"补光灯关闭确认: {response}")
                    self.waiting_for_response = False
                    return response
                elif command == "LON" and "LIGHT_ON_OK" in response:
                    logger.debug(f"补光灯开启确认(简化命令): {response}")
                    self.waiting_for_response = False
                    return response
                elif command == "LOFF" and "LIGHT_OFF_OK" in response:
                    logger.debug(f"补光灯关闭确认(简化命令): {response}")
                    self.waiting_for_response = False
                    return response
                elif command == "LT" and "LIGHT_TOGGLE_OK" in response:
                    logger.debug(f"补光灯切换确认: {response}")
                    self.waiting_for_response = False
                    return response

                # M1电机命令的响应检查
                elif command == "M1ON" and ("M1_AUTO_START_OK" in response or "M1_ALREADY_RUNNING" in response):
                    logger.debug(f"M1电机启动确认: {response}")
                    self.waiting_for_response = False
                    return response
                elif command == "M1OFF" and ("M1_AUTO_STOP_OK" in response or "M1_NOT_RUNNING" in response):
                    logger.debug(f"M1电机停止确认: {response}")
                    self.waiting_for_response = False
                    return response
                elif command == "M1FAST" and "M1_PRELIMINARY_OK" in response:
                    logger.debug(f"M1初赛模式确认: {response}")
                    self.waiting_for_response = False
                    return response
                elif command == "M1SLOW" and "M1_FINAL_OK" in response:
                    logger.debug(f"M1决赛模式确认: {response}")
                    self.waiting_for_response = False
                    return response
                elif command == "M1RESTART" and ("M1_RESTART_OK" in response or
                                                "M1_RESTART_ERROR_NOT_FINAL_MODE" in response or
                                                "M1_RESTART_ERROR_NOT_AUTO_MODE" in response):
                    logger.debug(f"M1重启命令确认: {response}")
                    self.waiting_for_response = False
                    return response

                # 其他命令的通用OK响应
                elif "OK" in response:
                    logger.debug(f"命令执行完成: {response}")
                    self.waiting_for_response = False
                    return response

                # 错误响应
                elif "ERROR" in response:
                    logger.error(f"命令执行错误: {response}")
                    self.waiting_for_response = False
                    return response

            time.sleep(0.01)  # 10ms检查间隔

        # 超时
        logger.warning(f"等待命令响应超时: {command}")
        self.waiting_for_response = False
        return "TIMEOUT"

    def send_command(self, command: str, timeout: float = 5.0) -> Optional[str]:
        """发送命令到ESP32并等待响应"""
        if not self.is_connected():
            logger.error("ESP32未连接")
            return None

        try:
            if self.simulation_mode:
                # 模拟模式 - 简单返回成功
                logger.debug(f"模拟发送命令: {command}")
                time.sleep(0.05)  # 模拟延迟
                return "OK"
            else:
                # 真实模式 - 等待真实响应
                command_line = f"{command}\n"
                self.serial_port.write(command_line.encode('utf-8'))
                self.serial_port.flush()

                logger.debug(f"发送命令: {command}")

                # 等待ESP32的真实响应
                response = self._wait_for_response(command, timeout)
                logger.debug(f"收到响应: {response}")
                return response

        except Exception as e:
            logger.error(f"发送命令失败: {e}")
            return None
    
    # 硬件控制方法
    def start_conveyor(self) -> bool:
        """启动传送带"""
        response = self.send_command("TRUN")
        return response is not None
    
    def stop_conveyor(self) -> bool:
        """停止传送带"""
        response = self.send_command("TS")
        return response is not None
    
    def reverse_conveyor(self) -> bool:
        """反转传送带"""
        response = self.send_command("TRUNB")
        return response is not None
    
    def turn_on_light(self) -> bool:
        """开启补光灯"""
        response = self.send_command("LIGHT_ON")
        return response is not None

    def turn_off_light(self) -> bool:
        """关闭补光灯"""
        response = self.send_command("LIGHT_OFF")
        return response is not None
    
    def control_m1_motor(self, enable: bool) -> bool:
        """控制M1电机"""
        command = "M1ON" if enable else "M1OFF"
        response = self.send_command(command)
        return response is not None

    def start_m1_preliminary_mode(self) -> bool:
        """启动M1初赛模式（快速运行）"""
        response = self.send_command("M1FAST")
        return response is not None and "M1_PRELIMINARY_OK" in str(response)

    def start_m1_final_mode(self) -> bool:
        """启动M1决赛模式（标准速度+自动控制）"""
        response = self.send_command("M1SLOW")
        return response is not None and "M1_FINAL_OK" in str(response)

    def restart_m1_final_mode(self) -> bool:
        """决赛模式下重启M1（分拣完成后使用）"""
        response = self.send_command("M1RESTART")
        return response is not None and "M1_RESTART_OK" in str(response)

    def toggle_light(self) -> bool:
        """切换补光灯状态"""
        response = self.send_command("LT")
        return response is not None and "LIGHT_TOGGLE_OK" in str(response)
    
    def sort_garbage(self, category: int) -> bool:
        """垃圾分类"""
        if 1 <= category <= 4:
            response = self.send_command(str(category))
            return response is not None
        return False
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        response = self.send_command("STATUS")
        if response:
            # 简化状态解析
            return {
                'connected': True,
                'status': 'OK'
            }
        return {}

    def simulate_full_detected(self):
        """模拟满载检测传感器触发"""
        if self.simulation_mode:
            self._handle_simulation_message("EVENT:FULL_DETECTED")
            logger.info("模拟满载检测传感器触发")
        else:
            logger.warning("只能在模拟模式下触发满载检测")

    def simulate_button_press(self):
        """模拟按钮按下"""
        if self.simulation_mode:
            self._handle_simulation_message("EVENT:BUTTON_PRESSED")
            logger.info("模拟按钮按下事件")
        else:
            logger.warning("只能在模拟模式下模拟按钮按下")

    def simulate_drop_detected(self):
        """模拟投放检测传感器触发"""
        if self.simulation_mode:
            self._handle_simulation_message("EVENT:DROP_DETECTED")
            logger.info("模拟投放检测传感器触发")
        else:
            logger.warning("只能在模拟模式下触发投放检测")
