#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能垃圾分类系统 - 简化GUI客户端
专注于核心功能：RKNN检测和自动分拣
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import json
import time
import cv2
from datetime import datetime
from PIL import Image, ImageTk
from ..hardware.orangepi_client import GarbageSorterClient
import logging
from ..detection.rknn_detector import RKNNDetector

logger = logging.getLogger(__name__)

class GarbageSorterGUI:
    """垃圾分拣系统GUI界面（简化版）"""

    def __init__(self, root, port: str = None):
        self.root = root
        
        # 加载配置
        self.load_config()
        
        self.root.title(self.config['window']['title'])

        # 检测屏幕分辨率并调整窗口大小
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()

        # 根据屏幕大小调整窗口尺寸（占屏幕的90%）
        window_width = min(self.config['window']['width'], int(screen_width * 0.9))
        window_height = min(self.config['window']['height'], int(screen_height * 0.9))

        # 计算缩放比例
        self.scale_factor = min(window_width / self.config['window']['width'],
                               window_height / self.config['window']['height'])

        self.root.geometry(f"{window_width}x{window_height}")
        self.root.resizable(True, True)

        logger.info(f"屏幕分辨率: {screen_width}x{screen_height}")
        logger.info(f"窗口大小: {window_width}x{window_height}")
        logger.info(f"缩放比例: {self.scale_factor:.2f}")
        
        # 键盘快捷键
        self.root.bind('<F11>', self.toggle_fullscreen)
        self.root.bind('<Escape>', lambda e: self.root.attributes('-fullscreen', False))
        self.is_fullscreen = False
        
        # 窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # 初始化核心组件
        self.client = GarbageSorterClient(port=port or self.config['hardware']['default_port'])
        # 初始化RKNN检测器
        self.detector = RKNNDetector(
            model_name=self.config['detection']['rknn_model'],
            img_size=640,
            conf_thres=self.config['detection']['confidence_threshold']
        )
        self.detector_type = "RKNN"
        logger.info("使用RKNN高性能检测器")

        # 系统状态
        self.is_connected = False
        self.detection_active = False
        self.video_mode = "promo"  # "promo" 或 "detection"

        # 检测状态管理 - 避免重复处理
        self.last_processed_detection_id = None
        self.detection_processing = False  # 正在处理检测结果
        self.last_detection_time = 0  # 最后检测时间
        self.last_detection = None  # 最后检测结果

        # 帧处理优化
        self.frame_skip_counter = 0
        self.frame_skip_interval = 2  # 每2帧处理一次，提高响应性

        # 垃圾跟踪状态 - 修正问题1和问题2
        self.current_garbage_tracking = False  # 是否正在跟踪特定垃圾
        self.tracking_garbage_id = None  # 当前跟踪的垃圾ID
        self.tracking_start_time = 0  # 开始跟踪的时间

        # 基于类型不变性的垃圾跟踪优化
        self.task_sequence = 0  # 任务序号，确保唯一性
        self.current_tracking_class = None  # 当前跟踪的垃圾类型

        # 掉落检测触发机制
        self.waiting_for_drop = True  # 是否等待掉落检测触发
        self.drop_detection_enabled = False  # 掉落检测是否启用
        
        # 比赛模式管理
        self.competition_mode = self.raw_config.get('system', {}).get('competition_mode', 'preliminary')
        self.competition_config = self.raw_config.get('competition', {})
        self.current_mode_config = self.competition_config.get(self.competition_mode, {})
        
        # 分拣状态管理（决赛模式用）
        self.sorting_in_progress = False
        self.belt_running = False

        # 位置监控状态
        self.monitoring_active = False
        self.monitoring_garbage_id = None
        self.monitoring_display_name = None
        self.monitoring_target_zone = None
        self.monitoring_class_name = None
        self.monitoring_start_time = 0
        self.monitoring_timeout = 10  # 默认10秒超时

        # 分拣统计
        self.classification_stats = {
            'recyclable': 0,
            'harmful': 0,
            'kitchen': 0,
            'other': 0
        }

        # 视频相关
        self.video_cap = None
        self.is_video_playing = False
        
        # 设置字体
        self.setup_fonts()

        # 创建界面
        self.create_widgets()

        # 初始化组件
        self.initialize_components()

    def load_config(self):
        """加载配置文件"""
        try:
            with open('config.json', 'r', encoding='utf-8') as f:
                raw_config = json.load(f)
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            raw_config = {}
        
        # 保存原始配置供后续使用
        self.raw_config = raw_config
        
        # 映射和整合配置
        self.config = {
            'window': {
                'width': 1024,  # 增加宽度适应Orange Pi
                'height': 768,  # 增加高度适应Orange Pi
                'title': '智能垃圾分拣系统'
            },
            'video': {
                'width': int(640 * getattr(self, 'scale_factor', 1.0)),
                'height': int(640 * getattr(self, 'scale_factor', 1.0)),  # 根据缩放比例调整
                'promo_path': raw_config.get('video', {}).get('promo_video_path', 'assets/promo_video.mp4'),
                'placeholder_text': '垃圾分类宣传视频\\n\\n请将垃圾放入检测区域'
            },
            'detection': {
                'confidence_threshold': raw_config.get('detection', {}).get('confidence_threshold', 0.4),
                'monitoring_interval': 500,
                # 'yolo_model': raw_config.get('detection', {}).get('yolo_model', 'yolov5s1'),  # 已移除YOLO支持
                'detector_type': raw_config.get('detection', {}).get('detector_type', 'auto'),
                'use_rknn_if_available': raw_config.get('detection', {}).get('use_rknn_if_available', True),
                'rknn_model': raw_config.get('detection', {}).get('rknn_model', 'rknn-0804.rknn')
            },
            'hardware': {
                'default_port': raw_config.get('serial', {}).get('port', '/dev/ttyACM0'),
                'reconnect_attempts': raw_config.get('system', {}).get('reconnect_attempts', 3)
            }
        }

    def setup_fonts(self):
        """设置字体，确保中文显示正常"""
        import platform
        import tkinter.font as tkFont

        # 根据操作系统选择合适的中文字体
        system = platform.system()
        if system == "Windows":
            # Windows系统字体
            self.default_font = ("Microsoft YaHei", 10)
            self.title_font = ("Microsoft YaHei", 12, "bold")
            self.large_font = ("Microsoft YaHei", 14, "bold")
        elif system == "Darwin":  # macOS
            # macOS系统字体
            self.default_font = ("PingFang SC", 10)
            self.title_font = ("PingFang SC", 12, "bold")
            self.large_font = ("PingFang SC", 14, "bold")
        else:  # Linux (包括Orange Pi)
            # Linux系统字体，优先使用中文字体
            available_fonts = tkFont.families()
            chinese_fonts = [
                "WenQuanYi Micro Hei",  # 文泉驿微米黑
                "WenQuanYi Zen Hei",    # 文泉驿正黑
                "Noto Sans CJK SC",     # Google Noto字体
                "Source Han Sans CN",   # 思源黑体
                "DejaVu Sans",          # DejaVu字体
                "Liberation Sans",      # Liberation字体
                "FreeSans",             # FreeSans字体
            ]

            # 选择第一个可用的中文字体
            selected_font = "TkDefaultFont"  # 默认字体
            for font in chinese_fonts:
                if font in available_fonts:
                    selected_font = font
                    break

            # 根据缩放比例调整字体大小
            scale = getattr(self, 'scale_factor', 1.0)
            self.default_font = (selected_font, max(8, int(10 * scale)))
            self.title_font = (selected_font, max(9, int(12 * scale)), "bold")
            self.large_font = (selected_font, max(10, int(14 * scale)), "bold")

        logger.info(f"设置字体: {self.default_font}")

    def toggle_fullscreen(self, event=None):
        """切换全屏模式"""
        self.is_fullscreen = not self.is_fullscreen
        self.root.attributes('-fullscreen', self.is_fullscreen)

    def create_widgets(self):
        """创建GUI组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)

        # 状态栏
        self.create_status_bar(main_frame)
        
        # 视频显示区域
        self.create_video_area(main_frame)
        
        # 控制按钮区域
        self.create_control_area(main_frame)
        
        # 统计信息区域
        self.create_stats_area(main_frame)

    def create_status_bar(self, parent):
        """创建状态栏"""
        status_frame = ttk.Frame(parent)
        status_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        # 系统状态
        self.system_status = tk.StringVar(value="系统就绪")
        ttk.Label(status_frame, text="状态:").pack(side=tk.LEFT)
        self.status_label = ttk.Label(status_frame, textvariable=self.system_status)
        self.status_label.pack(side=tk.LEFT, padx=(5, 20))
        
        # 检测器类型显示
        ttk.Label(status_frame, text="检测器:").pack(side=tk.LEFT)
        detector_color = "green" if hasattr(self, 'detector_type') and self.detector_type == "RKNN" else "blue"
        detector_text = getattr(self, 'detector_type', 'YOLO')
        detector_label = ttk.Label(status_frame, text=detector_text, foreground=detector_color)
        detector_label.pack(side=tk.LEFT, padx=(5, 20))

        # 控制按钮
        self.start_btn = ttk.Button(status_frame, text="启动系统", command=self.start_system)
        self.start_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.stop_btn = ttk.Button(status_frame, text="停止系统", command=self.stop_system, state=tk.DISABLED)
        self.stop_btn.pack(side=tk.LEFT)

    def create_video_area(self, parent):
        """创建视频显示区域"""
        video_frame = ttk.LabelFrame(parent, text="视频显示", padding="5")
        video_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        video_frame.columnconfigure(0, weight=1)
        video_frame.rowconfigure(0, weight=1)

        # 视频标签 - 统一使用像素尺寸
        self.video_label = tk.Label(video_frame,
                                   text="点击'播放视频'开始",
                                   bg="black",
                                   fg="white",
                                   font=self.title_font,
                                   compound='center')
        self.video_label.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 设置最小尺寸以确保一致性
        video_frame.grid_propagate(False)
        video_frame.configure(width=self.config['video']['width']+20, height=self.config['video']['height']+60)

        # 简化视频控制 - 移除多余按钮
        video_control_frame = ttk.Frame(video_frame)
        video_control_frame.grid(row=1, column=0, pady=(10, 0))

        # 硬件测试按钮
        self.hardware_test_btn = ttk.Button(video_control_frame, text="硬件测试", command=self.open_hardware_test)
        self.hardware_test_btn.pack(side=tk.LEFT)

    def create_control_area(self, parent):
        """创建控制区域"""
        control_frame = ttk.LabelFrame(parent, text="控制面板", padding="5")
        control_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(10, 0))

        # 比赛模式选择
        mode_frame = ttk.Frame(control_frame)
        mode_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(mode_frame, text="比赛模式:").pack(side=tk.LEFT)
        
        self.mode_var = tk.StringVar(value=self.competition_mode)
        mode_preliminary = ttk.Radiobutton(mode_frame, text="初赛模式", 
                                         variable=self.mode_var, value="preliminary",
                                         command=self.on_mode_changed)
        mode_preliminary.pack(side=tk.LEFT, padx=(10, 5))
        
        mode_final = ttk.Radiobutton(mode_frame, text="决赛模式", 
                                   variable=self.mode_var, value="final",
                                   command=self.on_mode_changed)
        mode_final.pack(side=tk.LEFT, padx=(5, 20))
        
        # 模式描述
        self.mode_desc = tk.StringVar()
        ttk.Label(mode_frame, textvariable=self.mode_desc, foreground='blue').pack(side=tk.LEFT)
        self.update_mode_description()
        
        # 补光灯控制
        light_frame = ttk.Frame(control_frame)
        light_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(light_frame, text="补光灯:").pack(side=tk.LEFT)
        
        # 补光灯状态
        self.light_status = False
        self.light_btn = ttk.Button(light_frame, text="开启补光灯", command=self.toggle_light)
        self.light_btn.pack(side=tk.LEFT, padx=(10, 5))
        
        # 补光灯状态显示
        self.light_status_var = tk.StringVar(value="关闭")
        light_status_label = ttk.Label(light_frame, textvariable=self.light_status_var, foreground='red')
        light_status_label.pack(side=tk.LEFT, padx=(5, 0))

        # 掉落检测测试按钮
        self.drop_test_btn = ttk.Button(light_frame, text="模拟掉落", command=self.test_drop_detection)
        self.drop_test_btn.pack(side=tk.LEFT, padx=(20, 5))

        # 检测状态
        status_frame = ttk.Frame(control_frame)
        status_frame.pack(fill=tk.X)
        
        self.detection_status = tk.StringVar(value="等待启动...")
        ttk.Label(status_frame, text="检测状态:").pack(side=tk.LEFT)
        ttk.Label(status_frame, textvariable=self.detection_status).pack(side=tk.LEFT, padx=(5, 20))

        # 检测结果
        self.detection_result = tk.StringVar(value="")
        ttk.Label(status_frame, text="检测结果:").pack(side=tk.LEFT)
        result_label = ttk.Label(status_frame, textvariable=self.detection_result, foreground='green')
        result_label.pack(side=tk.LEFT, padx=(5, 0))

    def create_stats_area(self, parent):
        """创建统计区域"""
        stats_frame = ttk.LabelFrame(parent, text="分拣统计", padding="5")
        stats_frame.grid(row=1, column=1, rowspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 统计变量
        self.stats_vars = {}
        garbage_types = [
            ("可回收垃圾", "recyclable"),
            ("有害垃圾", "harmful"), 
            ("厨余垃圾", "kitchen"),
            ("其他垃圾", "other")
        ]

        for i, (name, key) in enumerate(garbage_types):
            # 类型标签
            ttk.Label(stats_frame, text=f"{name}:").grid(row=i, column=0, sticky=tk.W, padx=(0, 10))
            
            # 数量显示
            self.stats_vars[key] = tk.StringVar(value="0")
            ttk.Label(stats_frame, textvariable=self.stats_vars[key], font=self.title_font).grid(row=i, column=1, sticky=tk.W)

        # 总计
        ttk.Separator(stats_frame, orient='horizontal').grid(row=len(garbage_types), column=0, columnspan=2, sticky=(tk.W, tk.E), pady=10)
        ttk.Label(stats_frame, text="总计:").grid(row=len(garbage_types)+1, column=0, sticky=tk.W, padx=(0, 10))
        self.stats_vars['total'] = tk.StringVar(value="0")
        ttk.Label(stats_frame, textvariable=self.stats_vars['total'], font=self.large_font).grid(row=len(garbage_types)+1, column=1, sticky=tk.W)

        # 重置按钮
        ttk.Button(stats_frame, text="重置统计", command=self.reset_stats).grid(row=len(garbage_types)+2, column=0, columnspan=2, pady=(20, 0))

    def initialize_components(self):
        """初始化组件"""
        # 在后台线程中初始化检测器
        threading.Thread(target=self.initialize_detector, daemon=True).start()
        
        # 连接硬件
        threading.Thread(target=self.connect_hardware, daemon=True).start()
        
        # 自动播放视频
        self.start_video()

    def initialize_detector(self):
        """初始化检测器模型"""
        try:
            self.log_message("正在初始化RKNN检测系统...")
            success = self.detector.load_model()
            if success:
                self.log_message("RKNN模型加载成功")
            else:
                self.log_message("RKNN模型加载失败")
        except Exception as e:
            self.log_message(f"RKNN初始化出错: {e}")

    def connect_hardware(self):
        """连接硬件"""
        try:
            self.log_message("正在连接硬件系统...")
            if self.client.connect():
                self.is_connected = True
                self.root.after(0, self.on_hardware_connected)
                self.log_message("硬件系统连接成功")
            else:
                self.log_message("硬件系统连接失败")
        except Exception as e:
            self.log_message(f"硬件连接出错: {e}")

    def on_mode_changed(self):
        """模式切换回调"""
        new_mode = self.mode_var.get()
        if new_mode != self.competition_mode:
            self.competition_mode = new_mode
            self.current_mode_config = self.competition_config.get(new_mode, {})
            self.update_mode_description()
            self.log_message(f"切换到{self.get_mode_display_name()}模式")
            
            # 如果系统正在运行，需要重置状态
            if self.detection_active:
                self.log_message("模式切换 - 重置系统状态")
                self.reset_detection_state()
                self.sorting_in_progress = False
                self.belt_running = False

    def update_mode_description(self):
        """更新模式描述"""
        desc = self.current_mode_config.get('description', '')
        self.mode_desc.set(f"({desc})")

    def get_mode_display_name(self):
        """获取模式显示名称"""
        return "初赛" if self.competition_mode == "preliminary" else "决赛"

    def is_preliminary_mode(self):
        """是否为初赛模式"""
        return self.competition_mode == "preliminary"

    def is_final_mode(self):
        """是否为决赛模式"""
        return self.competition_mode == "final"

    def on_hardware_connected(self):
        """硬件连接成功回调"""
        # 注册回调
        self.client.register_callback('start_button', self.on_start_button_pressed)
        self.client.register_callback('drop_detected', self.on_drop_detected)

    def start_system(self):
        """启动系统"""
        # 修改连接检查：允许模拟模式运行
        if not self.is_connected:
            # 检查是否有客户端对象（即使是模拟模式）
            if not hasattr(self, 'client') or self.client is None:
                messagebox.showerror("错误", "系统未初始化")
                return
            else:
                # 模拟模式可以运行，但给出提示
                self.log_message("硬件未连接，使用模拟模式运行")

        self.detection_active = True
        self.detection_processing = False  # 重置处理状态
        self.last_processed_detection_id = None  # 重置检测ID
        self.sorting_in_progress = False  # 重置分拣状态
        self.belt_running = False  # 重置传送带状态

        self.start_btn.config(state=tk.DISABLED)
        self.stop_btn.config(state=tk.NORMAL)

        # 优化1: 系统启动时延迟开启补光灯（确保ESP32准备就绪）
        if not self.light_status:
            self.log_message("系统启动 - 500ms后自动开启补光灯")
            # 延迟500ms发送开灯命令，确保ESP32完全准备好
            self.root.after(500, self._delayed_light_on)

        # 根据比赛模式启动M1
        m1_command = self.current_mode_config.get('m1_command', 'M1ON')  # 使用配置中的命令
        if self.is_preliminary_mode():
            # 初赛模式：M1快速运行
            self.log_message(f"初赛模式 - 启动M1快速运行 (命令: {m1_command})")
            self.client.send_command(m1_command)
        elif self.is_final_mode():
            # 决赛模式：M1标准运行+自动控制
            self.log_message(f"决赛模式 - 启动M1标准运行+自动控制 (命令: {m1_command})")
            self.client.send_command(m1_command)

        # 修复1: 启动系统但不立即开始检测，等待掉落检测触发
        self.waiting_for_drop = True
        self.drop_detection_enabled = True

        # 切换到检测模式（启动摄像头或模拟检测）
        self.switch_to_detection()

        # 启动检测器但不开始检测处理
        if hasattr(self.detector, 'start_detection'):
            detection_started = self.detector.start_detection()
            if detection_started:
                self.log_message(f"已启动{self.detector_type}检测器")
            else:
                self.log_message(f"{self.detector_type}检测器启动失败")

        mode_text = "硬件模式" if self.is_connected else "模拟模式"
        self.log_message(f"系统已启动 - {self.get_mode_display_name()}模式 - {mode_text}")
        self.log_message("⏳ 等待垃圾投放检测触发...")

    def _delayed_light_on(self):
        """延迟开启补光灯（确保ESP32准备就绪）"""
        try:
            if not self.light_status and hasattr(self, 'client') and self.client:
                self.log_message("发送补光灯开启命令")
                response = self.client.send_command("LIGHT_ON")
                if response and "OK" in str(response):
                    self.light_status = True
                    self.light_btn.config(text="关闭补光灯")
                    self.light_status_var.set("开启")
                    # 更改状态标签颜色为绿色
                    for widget in self.root.winfo_children():
                        self._update_light_status_color(widget, 'green')
                    self.log_message("补光灯已自动开启")
                else:
                    self.log_message(f"补光灯自动开启失败: {response}")
                    # 如果失败，再次尝试（可能ESP32还需要更多时间）
                    self.log_message("1秒后重试开启补光灯")
                    self.root.after(1000, self._retry_light_on)
        except Exception as e:
            self.log_message(f"延迟开灯出错: {e}")

    def _retry_light_on(self):
        """重试开启补光灯"""
        try:
            if not self.light_status and hasattr(self, 'client') and self.client:
                self.log_message("重试发送补光灯开启命令")
                response = self.client.send_command("LIGHT_ON")
                if response and "OK" in str(response):
                    self.light_status = True
                    self.light_btn.config(text="关闭补光灯")
                    self.light_status_var.set("开启")
                    # 更改状态标签颜色为绿色
                    for widget in self.root.winfo_children():
                        self._update_light_status_color(widget, 'green')
                    self.log_message("补光灯重试开启成功")
                else:
                    self.log_message(f"补光灯重试开启仍然失败: {response}")
        except Exception as e:
            self.log_message(f"重试开灯出错: {e}")

    def stop_system(self):
        """停止系统"""
        self.detection_active = False

        # 停止检测器
        if hasattr(self.detector, 'stop_detection'):
            self.detector.stop_detection()
            self.log_message(f"已停止{self.detector_type}检测器")

        # 重置检测状态
        self.detection_processing = False
        self.last_processed_detection_id = None
        self.last_detection_time = 0
        
        # 停止M1和M2
        self.log_message("停止M1辅助机构和M2传送带")
        self.client.send_command("M1OFF")
        self.client.send_command("TS")
        
        # 关闭补光灯
        if self.light_status:
            self.log_message("关闭补光灯")
            self.client.send_command("LIGHT_OFF")
            self.light_status = False
            self.light_btn.config(text="开启补光灯")
            self.light_status_var.set("关闭")
            for widget in self.root.winfo_children():
                self._update_light_status_color(widget, 'red')
        
        self.start_btn.config(state=tk.NORMAL)
        self.stop_btn.config(state=tk.DISABLED)
        
        # 切换回视频模式
        self.video_mode = "promo"
        self.start_video()
        
        self.log_message("系统已停止，检测状态已重置")

    def switch_to_detection(self):
        """切换到检测模式"""
        self.video_mode = "detection"
        self.stop_video()

        # 启动检测器（特别是RKNN检测器需要启动异步处理）
        if hasattr(self.detector, 'start_detection'):
            detection_started = self.detector.start_detection()
            if detection_started:
                self.log_message(f"已启动{self.detector_type}检测器")
            else:
                self.log_message(f"{self.detector_type}检测器启动失败")

        # 启动检测显示 - 支持模拟模式
        try:
            if self.client and hasattr(self.client, 'video_interface'):
                self.client.video_interface.set_frame_callback(self.process_detection_frame)
                self.client.start_camera()
                self.log_message("已切换到检测模式 - 摄像头已启动")

                # 实时检测模式：检测结果在process_detection_frame中直接处理
                self.log_message("实时检测模式已启动")
            else:
                # 模拟模式：创建模拟检测
                self.log_message("已切换到检测模式 - 模拟模式（无摄像头）")
                self.start_simulation_detection()
        except Exception as e:
            self.log_message(f"切换检测模式时出错: {e}")
            # 如果摄像头启动失败，也启动模拟检测
            self.start_simulation_detection()

    def start_simulation_detection(self):
        """启动模拟检测 - 用于无摄像头的情况"""
        try:
            self.log_message("启动模拟检测模式")

            # 启动模拟检测循环（包含画面更新）
            self.schedule_simulation_detection()

        except Exception as e:
            self.log_message(f"启动模拟检测失败: {e}")

    def create_simulation_frame(self):
        """创建模拟检测画面"""
        import numpy as np
        import cv2

        # 创建一个黑色背景图像
        placeholder_frame = np.zeros((self.config['video']['height'], self.config['video']['width'], 3), dtype=np.uint8)

        # 添加文字提示
        cv2.putText(placeholder_frame, "SIMULATION MODE", (180, 200),
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 255), 2)

        if self.waiting_for_drop:
            cv2.putText(placeholder_frame, "Waiting for drop...", (180, 240),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 0), 2)
        else:
            cv2.putText(placeholder_frame, "Detection Active", (180, 240),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)

        cv2.putText(placeholder_frame, "Put garbage here", (180, 280),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 1)

        return placeholder_frame

    # 监控方法已移除 - 现在使用实时检测单一路径
    # 检测结果在 process_detection_frame() 中直接处理

    def schedule_simulation_detection(self):
        """调度模拟检测"""
        if self.detection_active and self.video_mode == "detection":
            import random

            # 更新模拟画面显示
            simulation_frame = self.create_simulation_frame()

            # 在等待掉落状态下，只显示ROI区域
            if self.waiting_for_drop:
                display_frame = self.draw_roi_areas_for_roi(simulation_frame)
                self.update_video_display(display_frame)

                # 20%概率模拟掉落事件
                if self.drop_detection_enabled and random.random() < 0.2:
                    self.log_message("🎯 模拟掉落检测事件")
                    self.on_drop_detected()

            # 如果不在等待状态，进行正常的垃圾检测
            elif not self.detection_processing:
                display_frame = self.draw_roi_areas_for_roi(simulation_frame)
                self.update_video_display(display_frame)

                # 30%概率检测到垃圾
                if random.random() < 0.3:
                    self.simulate_garbage_detection()

            # 继续调度下一次检测
            self.root.after(2000, self.schedule_simulation_detection)  # 每2秒检测一次

    def simulate_garbage_detection(self):
        """模拟垃圾检测"""
        try:
            import random
            
            # 随机生成垃圾类型和位置
            garbage_types = ['harmful', 'recyclable', 'kitchen', 'other']
            class_name = random.choice(garbage_types)
            confidence = random.uniform(0.5, 0.9)
            
            # 随机位置（在检测区域内）
            x1 = random.randint(100, 400)
            y1 = random.randint(150, 300)
            x2 = x1 + random.randint(50, 100)
            y2 = y1 + random.randint(50, 100)
            
            # 构造检测结果
            simulated_results = [{
                'bbox': [x1, y1, x2, y2],
                'confidence': confidence,
                'class_name': class_name
            }]
            
            self.log_message(f"模拟检测: {class_name}, 置信度: {confidence:.2f}")

            # 设置last_detection供位置监控使用
            self.last_detection = simulated_results

            # 处理模拟检测结果
            self.handle_detection_results(simulated_results)
            
        except Exception as e:
            self.log_message(f"模拟检测出错: {e}")

    def reset_detection_state(self):
        """重置检测状态 - 用于分拣完成后"""
        self.detection_processing = False
        self.last_processed_detection_id = None
        self.last_detection_time = 0

        # 修正问题1: 重置垃圾跟踪状态
        self.current_garbage_tracking = False
        self.tracking_garbage_id = None
        self.tracking_start_time = 0
        self.current_tracking_class = None  # 重置跟踪的垃圾类型

        # 清除检测器的检测结果，避免检测框残留
        if hasattr(self.detector, 'clear_detection_results'):
            self.detector.clear_detection_results()

        # 修复1: 分拣完成后等待下一次掉落检测
        self.waiting_for_drop = True

        # 保持视频显示，但只显示ROI区域，不进行检测处理
        # 不停止视频，因为需要显示等待状态的ROI区域

        self.log_message("✅ 分拣任务完成，等待下一个垃圾投放...")

    def process_detection_frame(self, frame):
        """处理检测帧 - 修复1: 只在非等待掉落状态下进行检测"""
        try:
            # 帧跳过优化：在检测处理繁忙时跳过部分帧
            self.frame_skip_counter += 1
            should_process_detection = (self.frame_skip_counter % self.frame_skip_interval == 0)

            # 修复1: 检查是否在等待掉落状态
            if self.waiting_for_drop:
                # 等待掉落状态下，只显示ROI区域，不显示任何检测框
                display_frame = self.draw_roi_areas_for_roi(frame)
                self.update_video_display(display_frame)
                return

            if self.video_mode == "detection" and self.detection_active:
                # 优化：只在指定帧进行检测处理，但每帧都显示
                if should_process_detection:
                    # 进行RKNN检测
                    results = self.detector.detect_frame(frame)

                    # 设置last_detection供位置监控使用
                    self.last_detection = results if results else None

                    # 绘制结果（包含ROI区域和检测框）
                    if results:
                        self.log_message(f"检测到 {len(results)} 个对象")
                        display_frame = self.draw_detection_results_for_roi(frame, results)

                        # 实时处理检测结果 - 单一路径处理
                        self.handle_detection_results(results)
                    else:
                        # 即使没有检测结果，也要显示ROI区域
                        display_frame = self.draw_roi_areas_for_roi(frame)
                else:
                    # 跳过的帧只显示ROI区域，不进行检测
                    display_frame = self.draw_roi_areas_for_roi(frame)

                # 更新显示（会自动裁剪到ROI区域）
                self.update_video_display(display_frame)
            else:
                # 非检测模式，只显示ROI区域
                display_frame = self.draw_roi_areas_for_roi(frame)
                self.update_video_display(display_frame)
        except Exception as e:
            self.log_message(f"检测帧处理出错: {e}")

    def draw_detection_results(self, frame, detections):
        """绘制检测结果"""
        try:
            
            # 然后绘制检测结果
            colors = {
                'harmful': (0, 0, 255),
                'recyclable': (0, 255, 0),
                'kitchen': (255, 0, 0),
                'other': (0, 255, 255)
            }

            for detection in detections:
                bbox = detection.get('bbox', [])
                confidence = detection.get('confidence', 0.0)
                class_name = detection.get('class_name', 'unknown')

                if len(bbox) == 4:
                    x1, y1, x2, y2 = map(int, bbox)
                    color = colors.get(class_name, (255, 255, 255))
                    
                    # 绘制边界框
                    cv2.rectangle(frame, (x1, y1), (x2, y2), color, 2)
                    
                    # 绘制标签
                    label = f"{class_name}: {confidence:.2f}"
                    cv2.putText(frame, label, (x1, y1-10),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)

        except Exception as e:
            self.log_message(f"绘制检测结果出错: {e}")

        return frame

    def draw_detection_results_for_roi(self, frame, detections):
        """绘制检测结果 - 修复3: 直接在ROI区域绘制，避免坐标转换问题"""
        try:
            # 获取检测ROI区域
            roi_areas = self.detector.get_roi_areas()
            detection_roi = None
            for roi in roi_areas:
                if roi.get('purpose') == 'detection':
                    detection_roi = roi['area']
                    break

            if not detection_roi:
                # 如果没有ROI配置，使用默认ROI（640x640适配，下边界630）
                detection_roi = [0, 120, 640, 630]

            # 先裁剪到ROI区域
            roi_x1, roi_y1, roi_x2, roi_y2 = detection_roi
            frame_height, frame_width = frame.shape[:2]

            # 确保ROI坐标在帧范围内
            roi_x1 = max(0, min(roi_x1, frame_width))
            roi_y1 = max(0, min(roi_y1, frame_height))
            roi_x2 = max(roi_x1, min(roi_x2, frame_width))
            roi_y2 = max(roi_y1, min(roi_y2, frame_height))

            # 裁剪到ROI区域
            roi_frame = frame[roi_y1:roi_y2, roi_x1:roi_x2].copy()
            roi_height, roi_width = roi_frame.shape[:2]

            # 在ROI帧上绘制检测结果
            colors = {
                'harmful': (0, 0, 255),
                'recyclable': (0, 255, 0),
                'kitchen': (255, 0, 0),
                'other': (0, 255, 255)
            }

            for detection in detections:
                bbox = detection.get('bbox', [])
                confidence = detection.get('confidence', 0.0)
                class_name = detection.get('class_name', 'unknown')

                if len(bbox) == 4:
                    # 将原始坐标转换为ROI相对坐标
                    x1, y1, x2, y2 = map(int, bbox)

                    # 转换为ROI相对坐标
                    roi_rel_x1 = x1 - roi_x1
                    roi_rel_y1 = y1 - roi_y1
                    roi_rel_x2 = x2 - roi_x1
                    roi_rel_y2 = y2 - roi_y1

                    # 确保坐标在ROI帧范围内
                    roi_rel_x1 = max(0, min(roi_rel_x1, roi_width))
                    roi_rel_y1 = max(0, min(roi_rel_y1, roi_height))
                    roi_rel_x2 = max(roi_rel_x1, min(roi_rel_x2, roi_width))
                    roi_rel_y2 = max(roi_rel_y1, min(roi_rel_y2, roi_height))

                    # 检查转换后的坐标是否有效
                    if roi_rel_x2 <= roi_rel_x1 or roi_rel_y2 <= roi_rel_y1:
                        continue

                    color = colors.get(class_name, (255, 255, 255))

                    # 在ROI帧上绘制边界框
                    cv2.rectangle(roi_frame, (roi_rel_x1, roi_rel_y1), (roi_rel_x2, roi_rel_y2), color, 2)

                    # 绘制标签
                    label = f"{class_name}: {confidence:.2f}"
                    cv2.putText(roi_frame, label, (roi_rel_x1, max(10, roi_rel_y1-10)),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)

                    # 绘制位置信息
                    center_x = (roi_rel_x1 + roi_rel_x2) // 2
                    position_info = self.get_position_by_coordinates(center_x + roi_x1)  # 转换回原始坐标系
                    cv2.putText(roi_frame, f"位置: {position_info}", (roi_rel_x1, min(roi_height-10, roi_rel_y2+20)),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)

            # 在ROI帧上绘制区域边界和分界线
            roi_frame = self.draw_roi_areas_on_roi_frame(roi_frame, roi_width, roi_height)

            return roi_frame

        except Exception as e:
            self.log_message(f"绘制ROI检测结果出错: {e}")
            return frame

    def draw_roi_areas_on_full_frame(self, frame, detection_roi):
        """在完整帧上绘制ROI区域边界和分界线"""
        try:
            roi_x1, roi_y1, roi_x2, roi_y2 = detection_roi

            # 绘制ROI边界
            cv2.rectangle(frame, (roi_x1, roi_y1), (roi_x2, roi_y2), (255, 0, 0), 2)

            # 绘制中间分界线（左右分拣区域）
            center_x = (roi_x1 + roi_x2) // 2
            cv2.line(frame, (center_x, roi_y1), (center_x, roi_y2), (0, 255, 255), 2)

            # 绘制区域标签
            cv2.putText(frame, "左侧分拣区", (roi_x1 + 10, roi_y1 + 30),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            cv2.putText(frame, "右侧分拣区", (center_x + 10, roi_y1 + 30),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

            return frame

        except Exception as e:
            self.log_message(f"绘制ROI区域出错: {e}")
            return frame

    def draw_roi_areas_on_roi_frame(self, roi_frame, roi_width, roi_height):
        """在ROI帧上绘制区域边界 - 修复4: 正确的三区域布局"""
        try:
            # 计算三个区域的边界（三等分）
            left_boundary = roi_width // 3      # 左分拣区和检测区的分界线
            right_boundary = 2 * roi_width // 3  # 检测区和右分拣区的分界线

            # 绘制整体ROI边界
            cv2.rectangle(roi_frame, (0, 0), (roi_width-1, roi_height-1), (255, 255, 0), 2)

            # 绘制区域分界线
            cv2.line(roi_frame, (left_boundary, 0), (left_boundary, roi_height), (0, 255, 255), 2)
            cv2.line(roi_frame, (right_boundary, 0), (right_boundary, roi_height), (0, 255, 255), 2)

            # 绘制中间检测区域的边界（高亮显示）
            cv2.rectangle(roi_frame, (left_boundary, 0), (right_boundary, roi_height-1), (0, 255, 0), 2)

            # 绘制区域标签
            cv2.putText(roi_frame, "左分拣区", (10, 30),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            cv2.putText(roi_frame, "检测区", (left_boundary + 10, 30),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
            cv2.putText(roi_frame, "右分拣区", (right_boundary + 10, 30),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

            return roi_frame

        except Exception as e:
            self.log_message(f"绘制ROI区域出错: {e}")
            return roi_frame

    def draw_roi_areas_for_roi(self, frame):
        """绘制ROI区域 - 修复2: 使用与检测结果相同的绘制逻辑"""
        try:
            # 检查检测器是否可用
            if not hasattr(self.detector, 'get_roi_areas'):
                self.log_message("检测器没有get_roi_areas方法，使用默认ROI")
                # 使用默认ROI区域（640x640适配，下边界调整到630）
                detection_roi = (0, 120, 640, 630)
            else:
                # 获取ROI区域配置
                roi_areas = self.detector.get_roi_areas()
                detection_roi = None
                for roi in roi_areas:
                    if roi.get('purpose') == 'detection':
                        detection_roi = roi['area']
                        break

                if not detection_roi:
                    self.log_message("未找到检测ROI，使用默认ROI")
                    detection_roi = (0, 120, 640, 630)

            # 修复3: 直接在ROI区域绘制，避免坐标转换问题
            roi_x1, roi_y1, roi_x2, roi_y2 = detection_roi
            frame_height, frame_width = frame.shape[:2]

            # 确保ROI坐标在帧范围内
            roi_x1 = max(0, min(roi_x1, frame_width))
            roi_y1 = max(0, min(roi_y1, frame_height))
            roi_x2 = max(roi_x1, min(roi_x2, frame_width))
            roi_y2 = max(roi_y1, min(roi_y2, frame_height))

            # 先裁剪到ROI区域
            roi_frame = frame[roi_y1:roi_y2, roi_x1:roi_x2].copy()
            roi_height, roi_width = roi_frame.shape[:2]

            # 在ROI帧上绘制区域边界和分界线
            roi_frame = self.draw_roi_areas_on_roi_frame(roi_frame, roi_width, roi_height)

            return roi_frame
        except Exception as e:
            self.log_message(f"绘制ROI区域出错: {e}")
            return frame

    def get_position_by_coordinates(self, center_x):
        """根据X坐标获取位置信息 - 与RKNN检测器ROI配置保持一致"""
        # 统一使用RKNN检测器的ROI配置
        # 左分拣区：0-160，右分拣区：480-640，中间：160-480为检测区
        if center_x <= 160:
            return "左分拣区"
        elif center_x >= 480:
            return "右分拣区"
        else:
            return "检测区"

    def draw_roi_areas(self, frame):
        """绘制ROI感兴趣区域"""
        try:
            # 从RKNN检测器获取ROI配置
            roi_areas = self.detector.get_roi_areas()

            for roi in roi_areas:
                x1, y1, x2, y2 = roi['area']
                color = roi['color']
                name = roi['name']
                purpose = roi.get('purpose', '')

                # 绘制区域框
                cv2.rectangle(frame, (x1, y1), (x2, y2), color, 2)

                # 根据区域类型添加标签
                if purpose == 'detection':
                    # 检测区域 - 蓝色
                    cv2.putText(frame, "Detection Zone", (x1+5, y1-10),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)
                elif name == '左分拣区':
                    # 左分拣区域 - 绿色
                    cv2.putText(frame, "Left Sort", (x1+5, y1+15),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
                    cv2.putText(frame, "Harmful/Other", (x1+5, y1+35),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)
                elif name == '右分拣区':
                    # 右分拣区域 - 黄色
                    cv2.putText(frame, "Right Sort", (x1+5, y1+15),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
                    cv2.putText(frame, "Recycle/Kitchen", (x1+5, y1+35),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)

        except Exception as e:
            self.log_message(f"绘制ROI区域出错: {e}")

        return frame

    def handle_detection_results(self, results):
        """处理检测结果 - 统一处理入口，避免重复"""
        try:
            if not results or len(results) == 0:
                return
            
            # 获取置信度最高的检测结果
            best_detection = max(results, key=lambda x: x.get('confidence', 0.0))
            confidence = best_detection.get('confidence', 0.0)
            
            # 检查置信度阈值
            if confidence < self.config['detection']['confidence_threshold']:
                return
            
            # 优化方案: 基于垃圾类型不变性的ID生成
            class_name = best_detection.get('class_name', 'unknown')
            current_time = time.time()

            # 生成稳定的垃圾对象ID - 基于类型不变性
            garbage_object_id = self.generate_stable_garbage_id(class_name, current_time)

            # 检查ID生成是否成功
            if garbage_object_id is None:
                # ID生成失败（通常是检测到不同类型垃圾但正在跟踪其他垃圾）
                return

            # 修正问题1: 如果正在跟踪垃圾，只处理同一个垃圾对象
            if self.current_garbage_tracking:
                if garbage_object_id != self.tracking_garbage_id:
                    # 正在跟踪其他垃圾，跳过新检测
                    return
                else:
                    # 继续跟踪同一个垃圾，更新位置信息但不重新开始分拣
                    self.last_detection = results  # 更新位置信息用于监控
                    return

            # 避免重复处理同一个检测结果（短时间内的重复检测）
            if garbage_object_id == self.last_processed_detection_id:
                return
            
            # 标记正在处理，防止并发处理
            self.detection_processing = True
            self.last_processed_detection_id = garbage_object_id
            self.last_detection_time = time.time()

            # 修正问题1: 开始跟踪这个垃圾对象
            self.current_garbage_tracking = True
            self.tracking_garbage_id = garbage_object_id
            self.tracking_start_time = current_time
            self.current_tracking_class = class_name  # 记录当前跟踪的垃圾类型

            # 转换为标准格式并处理
            display_name = self.map_class_to_display_name(class_name)
            garbage_id = self.map_class_to_garbage_id(class_name)

            self.detection_result.set(f"检测到: {display_name} (置信度: {confidence:.2f})")
            self.log_message(f"新检测: {display_name}, 置信度: {confidence:.2f}, 垃圾对象ID: {garbage_object_id}")

            # 异步执行自动分拣，避免阻塞GUI线程
            self.root.after(10, lambda: self.auto_sort_garbage(garbage_id, display_name))
            
        except Exception as e:
            self.log_message(f"处理检测结果出错: {e}")
            self.detection_processing = False  # 出错时重置状态

    def generate_stable_garbage_id(self, class_name, current_time):
        """
        基于垃圾类型不变性生成稳定的垃圾对象ID
        核心思想: 垃圾类型在整个分拣过程中绝对不变
        """
        try:
            # 如果当前没有在跟踪垃圾，开始新的跟踪任务
            if not self.current_garbage_tracking:
                # 增加任务序号，确保唯一性
                self.task_sequence += 1
                # 生成基于类型和任务序号的稳定ID
                stable_id = f"{class_name}_task_{self.task_sequence}"
                self.log_message(f"生成新垃圾跟踪ID: {stable_id} (类型: {class_name})")
                return stable_id
            else:
                # 如果正在跟踪，检查是否是同一类型的垃圾
                if class_name == self.current_tracking_class:
                    # 同一类型，返回当前跟踪ID
                    return self.tracking_garbage_id
                else:
                    # 不同类型，说明检测到新垃圾，但当前正在跟踪其他垃圾
                    # 这种情况下应该忽略新检测
                    self.log_message(f"检测到不同类型垃圾({class_name})，但正在跟踪({self.current_tracking_class})，忽略")
                    return None

        except Exception as e:
            self.log_message(f"生成垃圾ID出错: {e}")
            # 出错时使用简单的时间戳方案
            return f"{class_name}_fallback_{int(current_time)}"

    def convert_full_to_roi_coords(self, bbox, roi_area):
        """
        修复2: 将完整帧坐标转换为ROI相对坐标
        Args:
            bbox: [x1, y1, x2, y2] 完整帧坐标
            roi_area: (x1, y1, x2, y2) ROI区域
        Returns:
            [x1, y1, x2, y2] ROI相对坐标
        """
        try:
            if len(bbox) != 4:
                return bbox

            roi_x1, roi_y1, roi_x2, roi_y2 = roi_area
            det_x1, det_y1, det_x2, det_y2 = bbox

            # 转换为ROI相对坐标
            roi_det_x1 = det_x1 - roi_x1
            roi_det_y1 = det_y1 - roi_y1
            roi_det_x2 = det_x2 - roi_x1
            roi_det_y2 = det_y2 - roi_y1

            return [roi_det_x1, roi_det_y1, roi_det_x2, roi_det_y2]
        except Exception as e:
            self.log_message(f"坐标转换出错: {e}")
            return bbox

    def is_bbox_in_roi(self, bbox, roi_area):
        """
        检查检测框是否在ROI区域内
        Args:
            bbox: [x1, y1, x2, y2] 完整帧坐标
            roi_area: (x1, y1, x2, y2) ROI区域
        Returns:
            bool: 是否在ROI内
        """
        try:
            if len(bbox) != 4:
                return False

            roi_x1, roi_y1, roi_x2, roi_y2 = roi_area
            det_x1, det_y1, det_x2, det_y2 = bbox

            # 检查检测框是否与ROI有交集
            return not (det_x2 <= roi_x1 or det_x1 >= roi_x2 or
                       det_y2 <= roi_y1 or det_y1 >= roi_y2)
        except Exception as e:
            self.log_message(f"ROI检查出错: {e}")
            return False

    def map_class_to_display_name(self, class_name):
        """映射类别名称到显示名称"""
        mapping = {
            'harmful': '有害垃圾',
            'recyclable': '可回收垃圾',
            'kitchen': '厨余垃圾',
            'other': '其他垃圾'
        }
        return mapping.get(class_name, '未知垃圾')

    def map_class_to_garbage_id(self, class_name):
        """映射类别名称到垃圾ID"""
        mapping = {
            'recyclable': 1,
            'harmful': 2,
            'kitchen': 3,
            'other': 4
        }
        return mapping.get(class_name, 0)

    def auto_sort_garbage(self, garbage_id, display_name):
        """自动分拣垃圾 - 支持初赛和决赛模式"""
        try:
            # 决赛模式：如果已经在分拣中，则跳过
            if self.is_final_mode() and self.sorting_in_progress:
                self.log_message(f"决赛模式 - 分拣进行中，跳过: {display_name}")
                self.detection_processing = False  # 重置检测状态
                return
            
            # 获取垃圾类型名称
            class_name = self.map_garbage_id_to_class_name(garbage_id)
            mode_text = self.get_mode_display_name()
            self.log_message(f"开始{mode_text}分拣: {display_name} (类型: {class_name})")
            
            # 决赛模式：标记分拣开始，停止M1
            if self.is_final_mode():
                self.sorting_in_progress = True
                self.log_message("决赛模式 - 分拣开始，停止M1，分拣完成后重启")
                # 停止M1辅助机构
                self.client.send_command("M1OFF")
            
            # 获取当前垃圾位置
            current_position = self.get_garbage_current_position()
            self.log_message(f"当前垃圾位置: {current_position}")
            
            # 确定目标区域和传送带命令
            if class_name in ['recyclable', 'kitchen']:
                target_zone = "右分拣区"
                target_command = "TRUN"   # 正转往可回收方向 (右分拣区)
            else:  # harmful, other
                target_zone = "左分拣区"
                target_command = "TRUNB"  # 倒转往有害方向 (左分拣区)
            
            self.log_message(f"目标区域: {target_zone}")
            
            # 智能传送带控制
            belt_action = self.determine_belt_action(current_position, target_zone, class_name)
            
            if belt_action == "already_in_target":
                self.log_message(f"{display_name} 已在目标区域 {target_zone}，直接执行分拣")
                self.execute_immediate_sorting(garbage_id, display_name)
            elif belt_action == "move_required":
                self.log_message(f"{display_name} 需要移动到 {target_zone}")
                self.log_message(f"发送传送带命令: {target_command}")
                self.log_message(f"传送带运行方向: {'正转(向右)' if target_command == 'TRUN' else '反转(向左)'}")

                # 标记传送带运行状态
                self.belt_running = True

                # 异步发送传送带命令，避免阻塞GUI
                def send_belt_command():
                    try:
                        response = self.client.send_command(target_command)

                        # 在GUI线程中处理响应
                        self.root.after(0, lambda: self.handle_belt_command_response(
                            response, garbage_id, display_name, target_zone, class_name))
                    except Exception as e:
                        self.root.after(0, lambda: self.log_message(f"传送带命令发送出错: {e}"))
                        self.root.after(0, self.reset_sorting_state)

                # 在后台线程中发送命令
                import threading
                threading.Thread(target=send_belt_command, daemon=True).start()
            else:
                self.log_message(f"无法确定传送带动作: {belt_action}")
                # 无法确定动作时重置状态
                self.reset_sorting_state()
                
        except Exception as e:
            self.log_message(f"自动分拣出错: {e}")
            # 出错时重置状态
            self.reset_sorting_state()

    def handle_belt_command_response(self, response, garbage_id, display_name, target_zone, class_name):
        """处理传送带命令响应 - 在GUI线程中执行"""
        try:
            # 修复：传送带命令返回状态消息，不是"OK"
            # 检查是否是有效的传送带响应
            valid_belt_responses = [
                "TRAPEZOIDAL_RUN_START",
                "TRAPEZOIDAL_RUN_BACKWARD_START",
                "TRAPEZOIDAL_STOP"
            ]

            response_str = str(response) if response else ""
            is_valid_response = any(valid_resp in response_str for valid_resp in valid_belt_responses)

            if response and is_valid_response:
                self.log_message(f"传送带启动成功: {response}")

                # 修正问题1: 不要立即重置检测状态！
                # 继续跟踪当前垃圾直到分拣完成
                self.log_message(f"继续跟踪垃圾对象: {self.tracking_garbage_id}")

                # 开始实时监控垃圾位置，而不是固定等待时间
                self.log_message(f"开始监控垃圾移动到{target_zone}...")
                self.start_position_monitoring(garbage_id, display_name, target_zone, class_name)
            else:
                self.log_message(f"传送带控制失败: {response}")
                # 传送带控制失败时重置状态
                self.reset_sorting_state()
        except Exception as e:
            self.log_message(f"处理传送带响应出错: {e}")
            self.reset_sorting_state()

    def start_position_monitoring(self, garbage_id, display_name, target_zone, class_name):
        """开始位置监控，实时检测垃圾是否到达目标区域"""
        try:
            self.log_message(f"开始位置监控: {display_name} -> {target_zone}")

            # 设置监控参数
            self.monitoring_active = True
            self.monitoring_garbage_id = garbage_id
            self.monitoring_display_name = display_name
            self.monitoring_target_zone = target_zone
            self.monitoring_class_name = class_name
            self.monitoring_start_time = time.time()
            self.monitoring_timeout = 15  # 优化6: 增加超时时间到15秒

            # 开始监控循环
            self.monitor_position()

        except Exception as e:
            self.log_message(f"启动位置监控失败: {e}")
            self.reset_sorting_state()

    def monitor_position(self):
        """监控垃圾位置的循环方法 - 优化版本"""
        try:
            if not self.monitoring_active:
                return

            # 优化4: 增加超时时间到15秒，给传送带更多时间
            if time.time() - self.monitoring_start_time > self.monitoring_timeout:
                self.log_message(f"位置监控超时({self.monitoring_timeout}秒)，强制停止传送带并执行分拣")
                # 超时时强制停止传送带并执行分拣
                self.client.send_command("TS")  # 停止传送带
                self.stop_position_monitoring()
                # 即使超时也执行分拣，避免垃圾卡住
                self.execute_immediate_sorting(self.monitoring_garbage_id, self.monitoring_display_name)
                return

            # 获取当前位置
            current_position = self.get_garbage_current_position()

            # 优化5: 增加位置检测的容错性
            if current_position == self.monitoring_target_zone:
                self.log_message(f"✅ 垃圾已到达目标区域: {self.monitoring_target_zone}")
                self.stop_position_monitoring()
                # 立即执行分拣
                self.execute_immediate_sorting(self.monitoring_garbage_id, self.monitoring_display_name)
                return
            elif current_position == "未知位置":
                # 如果检测不到位置，可能是垃圾已经移出检测区域
                elapsed_time = time.time() - self.monitoring_start_time
                if elapsed_time > 3:  # 3秒后如果检测不到位置，认为可能已到达
                    self.log_message(f"⚠️ 检测不到垃圾位置(已运行{elapsed_time:.1f}秒)，可能已到达目标区域，执行分拣")
                    self.stop_position_monitoring()
                    self.execute_immediate_sorting(self.monitoring_garbage_id, self.monitoring_display_name)
                    return

            # 如果还没到达，继续监控
            elapsed_time = time.time() - self.monitoring_start_time
            self.log_message(f"🔍 位置监控({elapsed_time:.1f}s): 当前={current_position}, 目标={self.monitoring_target_zone}")

            # 检测传送带运行效果
            if hasattr(self, 'last_center_x') and hasattr(self, 'last_detection_time'):
                current_detection = self.last_detection
                if current_detection and len(current_detection) > 0:
                    bbox = current_detection[0]['bbox']
                    current_center_x = (bbox[0] + bbox[2]) // 2
                    time_diff = time.time() - self.last_detection_time

                    if time_diff > 0.5:  # 至少0.5秒间隔
                        x_movement = current_center_x - self.last_center_x
                        if abs(x_movement) < 5:  # 位置变化小于5像素
                            self.log_message(f"⚠️ 传送带可能未运行: {time_diff:.1f}s内X坐标仅变化{x_movement}px")
                        else:
                            direction = "向右" if x_movement > 0 else "向左"
                            self.log_message(f"✅ 传送带运行正常: {time_diff:.1f}s内{direction}移动{abs(x_movement)}px")

                        self.last_center_x = current_center_x
                        self.last_detection_time = time.time()
                else:
                    # 记录当前位置作为基准
                    if current_detection and len(current_detection) > 0:
                        bbox = current_detection[0]['bbox']
                        self.last_center_x = (bbox[0] + bbox[2]) // 2
                        self.last_detection_time = time.time()

            # 每500ms检查一次位置，减少对GUI的影响
            self.root.after(500, self.monitor_position)

        except Exception as e:
            self.log_message(f"位置监控出错: {e}")
            self.stop_position_monitoring()

    def stop_position_monitoring(self):
        """停止位置监控"""
        try:
            self.monitoring_active = False
            self.log_message("位置监控已停止")
        except Exception as e:
            self.log_message(f"停止位置监控出错: {e}")

    def reset_sorting_state(self):
        """重置分拣相关状态"""
        self.detection_processing = False
        self.sorting_in_progress = False
        self.belt_running = False

        # 修正问题1: 重置垃圾跟踪状态
        self.current_garbage_tracking = False
        self.tracking_garbage_id = None
        self.tracking_start_time = 0
        self.current_tracking_class = None  # 重置跟踪的垃圾类型

        # 停止位置监控
        self.stop_position_monitoring()

        # 决赛模式的特殊处理
        if self.is_final_mode():
            self.log_message("决赛模式 - 分拣状态已重置，停止垃圾跟踪，可处理下一个垃圾")
        else:
            self.log_message("分拣状态已重置，停止垃圾跟踪，可处理下一个垃圾")

    def map_garbage_id_to_class_name(self, garbage_id):
        """将垃圾ID映射为类别名称"""
        mapping = {
            1: 'recyclable',   # 可回收垃圾
            2: 'harmful',      # 有害垃圾
            3: 'kitchen',      # 厨余垃圾
            4: 'other'         # 其他垃圾
        }
        return mapping.get(garbage_id, 'unknown')

    def get_garbage_current_position(self):
        """获取垃圾当前位置 - 优化版本"""
        try:
            # 优化2: 获取实时检测结果，而不是缓存的结果
            if hasattr(self, 'last_detection') and self.last_detection:
                # 使用最新的检测结果
                detection_results = self.last_detection
                if isinstance(detection_results, list) and len(detection_results) > 0:
                    # 获取置信度最高的检测结果
                    detection = max(detection_results, key=lambda x: x.get('confidence', 0.0))
                else:
                    detection = detection_results
            else:
                # 备用方案：从检测器获取
                detection = self.detector.get_last_detection()
                if not detection:
                    return "未知位置"

            # 处理检测结果格式
            if isinstance(detection, dict) and 'bbox' in detection:
                bbox = detection['bbox']
            elif isinstance(detection, list) and len(detection) > 0:
                bbox = detection[0].get('bbox', [])
            else:
                return "未知位置"

            if len(bbox) != 4:
                return "未知位置"

            # 计算中心点
            x1, y1, x2, y2 = bbox
            center_x = (x1 + x2) // 2
            _ = (y1 + y2) // 2  # center_y暂时不使用

            # 优化3: 更精确的位置判断 (640宽度，与RKNN检测器ROI配置一致)
            # 左分拣区域: 0-160 (25%)
            # 检测区域: 160-480 (50%)
            # 右分拣区域: 480-640 (25%)
            if center_x <= 160:
                position = "左分拣区"
            elif center_x >= 480:
                position = "右分拣区"
            else:
                position = "检测区"

            # 添加调试信息
            self.log_message(f"位置检测: 中心点X={center_x}, 判定位置={position}")

            # 添加区域边界调试信息
            if position == "检测区":
                distance_to_left = center_x - 160
                distance_to_right = 480 - center_x
                self.log_message(f"检测区位置详情: 距左分拣区边界{distance_to_left}px, 距右分拣区边界{distance_to_right}px")

            return position

        except Exception as e:
            self.log_message(f"获取位置失败: {e}")
            return "未知位置"

    def determine_belt_action(self, current_position, target_zone, class_name):
        """确定传送带动作"""
        try:
            self.log_message(f"位置分析: 当前={current_position}, 目标={target_zone}, 类型={class_name}")

            # 如果已经在目标区域
            if current_position == target_zone:
                return "already_in_target"

            # 如果在错误的分拣区
            if current_position in ["左分拣区", "右分拣区"] and current_position != target_zone:
                self.log_message(f"垃圾在错误分拣区: {current_position} → 需要移动到 {target_zone}")
                return "move_required"

            # 如果在检测区，需要移动到目标分拣区
            if current_position == "检测区":
                self.log_message(f"垃圾在中间区域 → 需要移动到 {target_zone}")
                return "move_required"

            # 其他情况
            return "move_required"

        except Exception as e:
            self.log_message(f"传送带动作判断失败: {e}")
            return "unknown"

    def execute_immediate_sorting(self, garbage_id, display_name):
        """立即执行分拣（垃圾已在目标区域）"""
        try:
            mode_text = self.get_mode_display_name()
            self.log_message(f"{mode_text}立即分拣: {display_name}")

            # 停止传送带
            self.log_message("停止传送带")
            stop_response = self.client.send_command("TS")

            # 检查ESP32硬件是否连接
            hardware_connected = self.client and hasattr(self.client, 'is_connected') and self.client.is_connected

            if stop_response and "OK" in stop_response:
                self.log_message("传送带已停止")
                self.belt_running = False
            elif not hardware_connected:
                # 模拟模式：没有硬件连接，模拟传送带停止
                self.log_message("模拟模式 - 传送带已停止")
                self.belt_running = False

            # 使用非阻塞方式等待传送带停止
            self.log_message("等待传送带停止...")
            self.root.after(1000, lambda: self._execute_sorting_action(garbage_id, display_name))

        except Exception as e:
            self.log_message(f"立即分拣出错: {e}")
            self.reset_sorting_state()  # 出错时重置状态

    def _execute_sorting_action(self, garbage_id, display_name):
        """执行实际的分拣动作（传送带停止后）"""
        try:
            # 执行分拣
            self.log_message(f"执行分拣动作: {display_name} (命令: {garbage_id})")
            sort_response = self.client.send_command(str(garbage_id))

            # 检查ESP32硬件是否连接
            hardware_connected = self.client and hasattr(self.client, 'is_connected') and self.client.is_connected

            if sort_response and "OK" in sort_response:
                self.log_message(f"分拣完成: {display_name}")
                self.update_classification_stats(garbage_id)

                # ESP32已发送OK确认，分拣动作已完全完成，立即进入下一阶段
                self.log_message("ESP32分拣动作已完成，准备下一任务")
                self.complete_sorting_task(display_name)

            elif not hardware_connected:
                # 模拟模式：没有硬件连接，模拟分拣成功
                self.log_message(f"模拟模式 - 分拣完成: {display_name}")
                self.update_classification_stats(garbage_id)

                # 模拟等待时间后完成任务
                self.log_message("模拟等待分拣动作完成...")
                self.root.after(2000, lambda: self.complete_sorting_task(display_name))

            elif sort_response == "TIMEOUT":
                self.log_message(f"分拣命令超时: {display_name}")
                # 超时时重置所有状态，准备下一次检测
                self.reset_sorting_state()
                self.reset_detection_state()
                # 重新进入等待掉落状态
                self.waiting_for_drop = True

            else:
                self.log_message(f"分拣失败: {display_name} (响应: {sort_response})")
                # 分拣失败时重置所有状态，准备下一次检测
                self.reset_sorting_state()
                self.reset_detection_state()
                # 重新进入等待掉落状态
                self.waiting_for_drop = True

        except Exception as e:
            self.log_message(f"分拣动作执行出错: {e}")
            # 出错时重置所有状态，准备下一次检测
            self.reset_sorting_state()
            self.reset_detection_state()
            # 重新进入等待掉落状态
            self.waiting_for_drop = True

    def complete_sorting_task(self, display_name):
        """完成分拣任务，准备进行下一次任务"""
        try:
            self.log_message(f"分拣任务完成: {display_name}")

            # 根据比赛模式进行不同的后处理
            if self.is_preliminary_mode():
                # 初赛模式：立即重置所有状态，准备下一次检测
                self.log_message("初赛模式 - 任务完成，重置状态，准备下一次检测")
                self.reset_detection_state()
                self.reset_sorting_state()
                # 重新进入等待掉落状态
                self.waiting_for_drop = True
                self.log_message("✅ 系统已准备就绪，等待下一个垃圾投放...")

            elif self.is_final_mode():
                # 决赛模式：重置分拣状态，然后重启M1
                self.log_message("决赛模式 - 任务完成，重启M1...")
                self.reset_sorting_state()
                self.restart_m1_after_sorting()

            self.log_message("=== 分拣任务完全完成，系统准备就绪 ===")

        except Exception as e:
            self.log_message(f"完成分拣任务出错: {e}")
            self.reset_sorting_state()

    def restart_m1_after_sorting(self):
        """决赛模式：分拣完成后重启M1"""
        try:
            # 获取重启命令
            restart_command = self.current_mode_config.get('m1_restart_command', 'M1RESTART')
            self.log_message(f"决赛模式 - 重启M1辅助机构... (命令: {restart_command})")
            
            # 重启M1辅助机构
            restart_response = self.client.send_command(restart_command)
            
            if restart_response and "OK" in restart_response:
                self.log_message("决赛模式 - M1已重启")
                
                # M1重启后，延迟2秒重置检测状态
                self.root.after(2000, self.complete_final_mode_cycle)
            else:
                self.log_message(f"决赛模式 - M1重启失败: {restart_response}")
                # 重启失败时也要重置状态
                self.reset_sorting_state()
                
        except Exception as e:
            self.log_message(f"决赛模式M1重启出错: {e}")
            self.reset_sorting_state()

    def complete_final_mode_cycle(self):
        """完成决赛模式的完整分拣周期"""
        self.log_message("决赛模式 - 分拣周期完成，准备处理下一个垃圾")
        self.reset_detection_state()  # 重置检测状态
        self.sorting_in_progress = False  # 清除分拣进行标志
        # 重新进入等待掉落状态
        self.waiting_for_drop = True
        self.log_message("✅ 决赛模式系统已准备就绪，等待下一个垃圾投放...")

    def update_classification_stats(self, garbage_id):
        """更新分类统计"""
        try:
            id_to_key = {1: "recyclable", 2: "harmful", 3: "kitchen", 4: "other"}
            stats_key = id_to_key.get(garbage_id)
            
            if stats_key and stats_key in self.stats_vars:
                # 更新计数
                current = int(self.stats_vars[stats_key].get())
                self.stats_vars[stats_key].set(str(current + 1))
                
                # 更新总计
                total = sum(int(self.stats_vars[key].get()) for key in ["recyclable", "harmful", "kitchen", "other"])
                self.stats_vars["total"].set(str(total))
                
        except Exception as e:
            self.log_message(f"统计更新失败: {e}")

    def reset_stats(self):
        """重置统计"""
        for key in ["recyclable", "harmful", "kitchen", "other", "total"]:
            if key in self.stats_vars:
                self.stats_vars[key].set("0")
        self.log_message("统计已重置")

    def start_video(self):
        """开始播放视频"""
        if not self.is_video_playing:
            self.is_video_playing = True
            threading.Thread(target=self.video_loop, daemon=True).start()
            self.log_message("视频播放开始")

    def stop_video(self):
        """停止播放视频"""
        self.is_video_playing = False
        if self.video_cap:
            self.video_cap.release()
            self.video_cap = None

    def video_loop(self):
        """视频播放循环"""
        try:
            video_path = self.config['video']['promo_path']
            
            # 检查文件是否存在
            import os
            if not os.path.exists(video_path):
                self.show_placeholder_video()
                return

            self.video_cap = cv2.VideoCapture(video_path)
            if not self.video_cap.isOpened():
                self.show_placeholder_video()
                return

            fps = int(self.video_cap.get(cv2.CAP_PROP_FPS))
            frame_delay = 1.0 / fps if fps > 0 else 1.0 / 30

            while self.is_video_playing:
                ret, frame = self.video_cap.read()
                
                if not ret:
                    # 重新开始播放
                    self.video_cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
                    continue

                self.update_video_display(frame)
                time.sleep(frame_delay)

        except Exception as e:
            self.log_message(f"视频播放出错: {e}")
            self.show_placeholder_video()
        finally:
            if self.video_cap:
                self.video_cap.release()
                self.video_cap = None

    def show_placeholder_video(self):
        """显示占位符视频 - 统一尺寸"""
        def update_placeholder():
            self.video_label.config(
                text=self.config['video']['placeholder_text'],
                image='',
                bg='black',
                fg='white',
                compound='center',
                justify='center'
            )
            # 清除图像引用
            self.video_label.image = None
        
        self.root.after(0, update_placeholder)

    def update_video_display(self, frame):
        """更新视频显示 - 修复2: 简化ROI显示流程"""
        try:
            # 修复2: frame已经是处理好的ROI帧，直接显示
            # 统一调整尺寸到配置的视频尺寸
            target_width = self.config['video']['width']
            target_height = self.config['video']['height']

            # 优化：只在尺寸不匹配时进行缩放
            frame_height, frame_width = frame.shape[:2]
            if frame_width != target_width or frame_height != target_height:
                # 使用更快的插值方法
                frame = cv2.resize(frame, (target_width, target_height), interpolation=cv2.INTER_LINEAR)

            frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

            # 转换为tkinter图像 - 优化内存管理
            pil_image = Image.fromarray(frame)
            tk_image = ImageTk.PhotoImage(pil_image)

            # 更新显示 - 确保图像占满整个标签
            def update_label():
                # 释放之前的图像引用，避免内存泄漏
                if hasattr(self.video_label, 'image') and self.video_label.image:
                    try:
                        del self.video_label.image
                    except:
                        pass

                self.video_label.config(image=tk_image, text="", compound='center')
                self.video_label.image = tk_image

            self.root.after(0, update_label)

        except Exception as e:
            self.log_message(f"视频显示更新出错: {e}")  # 显示错误信息以便调试

    # 事件回调
    def on_start_button_pressed(self):
        """启动按钮按下回调"""
        if not self.detection_active:
            self.start_system()

    def on_drop_detected(self):
        """垃圾投放检测回调 - 修复1: 实现掉落检测触发机制"""
        self.log_message("🎯 检测到垃圾投放事件")

        # 检查是否在等待掉落状态
        if self.waiting_for_drop and self.drop_detection_enabled and self.detection_active:
            self.log_message("✅ 掉落检测触发，开始垃圾检测...")

            # 停止等待掉落状态，开始检测
            self.waiting_for_drop = False

            # 视频已经在运行，只需要开始检测处理
            # 不需要重新启动视频，因为在start_system中已经启动了

            self.log_message("🔍 垃圾检测已启动，等待识别结果...")
        else:
            if not self.detection_active:
                self.log_message("⚠️ 系统未启动，忽略掉落事件")
            elif not self.waiting_for_drop:
                self.log_message("⚠️ 当前正在处理垃圾，忽略新的掉落事件")
            else:
                self.log_message("⚠️ 掉落检测未启用，忽略事件")

    def test_drop_detection(self):
        """测试掉落检测 - 手动触发掉落事件"""
        if self.detection_active:
            self.log_message("🧪 手动触发掉落检测测试")
            self.on_drop_detected()
        else:
            self.log_message("⚠️ 系统未启动，无法测试掉落检测")

    def log_message(self, message):
        """记录日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        
        # 更新状态显示
        if hasattr(self, 'detection_status'):
            self.detection_status.set(message[:50] + "..." if len(message) > 50 else message)

        # 打印到控制台
        print(log_entry)

    def open_hardware_test(self):
        """打开硬件测试界面"""
        try:
            # 创建硬件测试窗口（无论是否连接都可以打开）
            test_window = tk.Toplevel(self.root)
            test_window.title("ESP32硬件测试")
            test_window.geometry("400x300")
            test_window.resizable(False, False)
            
            # 窗口居中
            test_window.transient(self.root)
            test_window.grab_set()
            
            # 创建测试界面
            main_frame = ttk.Frame(test_window, padding="10")
            main_frame.pack(fill=tk.BOTH, expand=True)
            
            # 连接状态
            status_frame = ttk.LabelFrame(main_frame, text="连接状态", padding="10")
            status_frame.pack(fill=tk.X, pady=(0, 10))
            
            # 获取端口信息（处理可能不存在的情况）
            port_info = "未知"
            try:
                if hasattr(self.client, 'esp32_controller') and hasattr(self.client.esp32_controller, 'port'):
                    port_info = self.client.esp32_controller.port
                elif hasattr(self.client, 'port'):
                    port_info = self.client.port
            except:
                port_info = "无法获取"
            
            ttk.Label(status_frame, text=f"串口: {port_info}").pack(anchor=tk.W)
            ttk.Label(status_frame, text=f"状态: {'已连接' if self.is_connected else '未连接'}").pack(anchor=tk.W)
            
            # 传送带测试
            belt_frame = ttk.LabelFrame(main_frame, text="传送带测试", padding="10")
            belt_frame.pack(fill=tk.X, pady=(0, 10))
            
            belt_btn_frame = ttk.Frame(belt_frame)
            belt_btn_frame.pack()
            
            ttk.Button(belt_btn_frame, text="正转(TRUN)", 
                      command=lambda: self.test_command("TRUN")).pack(side=tk.LEFT, padx=(0, 5))
            ttk.Button(belt_btn_frame, text="反转(TRUNB)", 
                      command=lambda: self.test_command("TRUNB")).pack(side=tk.LEFT, padx=(0, 5))
            ttk.Button(belt_btn_frame, text="停止(TS)", 
                      command=lambda: self.test_command("TS")).pack(side=tk.LEFT)
            
            # 分拣测试
            sort_frame = ttk.LabelFrame(main_frame, text="分拣测试", padding="10")
            sort_frame.pack(fill=tk.X, pady=(0, 10))
            
            sort_btn_frame = ttk.Frame(sort_frame)
            sort_btn_frame.pack()
            
            ttk.Button(sort_btn_frame, text="可回收(1)", 
                      command=lambda: self.test_command("1")).pack(side=tk.LEFT, padx=(0, 5))
            ttk.Button(sort_btn_frame, text="有害(2)", 
                      command=lambda: self.test_command("2")).pack(side=tk.LEFT, padx=(0, 5))
            ttk.Button(sort_btn_frame, text="厨余(3)", 
                      command=lambda: self.test_command("3")).pack(side=tk.LEFT, padx=(0, 5))
            ttk.Button(sort_btn_frame, text="其他(4)", 
                      command=lambda: self.test_command("4")).pack(side=tk.LEFT)
            
            # 补光灯测试
            light_frame = ttk.LabelFrame(main_frame, text="补光灯测试", padding="10")
            light_frame.pack(fill=tk.X, pady=(0, 10))
            
            light_btn_frame = ttk.Frame(light_frame)
            light_btn_frame.pack()
            
            ttk.Button(light_btn_frame, text="开启(LIGHT_ON)",
                      command=lambda: self.test_command("LIGHT_ON")).pack(side=tk.LEFT, padx=(0, 5))
            ttk.Button(light_btn_frame, text="关闭(LIGHT_OFF)",
                      command=lambda: self.test_command("LIGHT_OFF")).pack(side=tk.LEFT)
            
            # 测试结果显示
            result_frame = ttk.LabelFrame(main_frame, text="测试结果", padding="10")
            result_frame.pack(fill=tk.BOTH, expand=True)
            
            self.test_result_text = tk.Text(result_frame, height=6, wrap=tk.WORD)
            scrollbar = ttk.Scrollbar(result_frame, orient=tk.VERTICAL, command=self.test_result_text.yview)
            self.test_result_text.configure(yscrollcommand=scrollbar.set)
            
            self.test_result_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            
            # 关闭按钮
            ttk.Button(main_frame, text="关闭", command=test_window.destroy).pack(pady=(10, 0))
            
            self.log_message("硬件测试界面已打开")
            
        except Exception as e:
            self.log_message(f"打开硬件测试界面失败: {e}")
            messagebox.showerror("错误", f"无法打开硬件测试界面: {e}")

    def test_command(self, command):
        """执行测试命令"""
        try:
            if not self.is_connected:
                # 即使未连接也显示命令尝试，便于调试
                timestamp = datetime.now().strftime("%H:%M:%S")
                error_msg = f"[{timestamp}] 命令: {command} -> 失败: 硬件未连接\n"
                if hasattr(self, 'test_result_text'):
                    self.test_result_text.insert(tk.END, error_msg)
                    self.test_result_text.see(tk.END)
                self.log_message(f"测试命令失败 - 硬件未连接: {command}")
                return
            
            self.log_message(f"发送测试命令: {command}")
            response = self.client.send_command(command)
            
            # 在测试结果窗口显示
            if hasattr(self, 'test_result_text'):
                timestamp = datetime.now().strftime("%H:%M:%S")
                result_text = f"[{timestamp}] 命令: {command} -> 响应: {response}\n"
                self.test_result_text.insert(tk.END, result_text)
                self.test_result_text.see(tk.END)
            
            if response and "OK" in str(response):
                self.log_message(f"命令执行成功: {command}")
            else:
                self.log_message(f"命令执行失败: {command}, 响应: {response}")
                
        except Exception as e:
            error_msg = f"测试命令失败: {command}, 错误: {e}"
            self.log_message(error_msg)
            if hasattr(self, 'test_result_text'):
                timestamp = datetime.now().strftime("%H:%M:%S")
                self.test_result_text.insert(tk.END, f"[{timestamp}] 错误: {error_msg}\n")
                self.test_result_text.see(tk.END)

    def toggle_light(self):
        """切换补光灯状态"""
        try:
            if not self.light_status:
                # 开启补光灯
                self.log_message("开启补光灯")
                response = self.client.send_command("LIGHT_ON")
                
                if response and "OK" in str(response):
                    self.light_status = True
                    self.light_btn.config(text="关闭补光灯")
                    self.light_status_var.set("开启")
                    # 更改状态标签颜色为绿色
                    for widget in self.root.winfo_children():
                        self._update_light_status_color(widget, 'green')
                    self.log_message("补光灯已开启")
                else:
                    self.log_message(f"补光灯开启失败: {response}")
            else:
                # 关闭补光灯
                self.log_message("关闭补光灯")
                response = self.client.send_command("LIGHT_OFF")
                
                if response and "OK" in str(response):
                    self.light_status = False
                    self.light_btn.config(text="开启补光灯")
                    self.light_status_var.set("关闭")
                    # 更改状态标签颜色为红色
                    for widget in self.root.winfo_children():
                        self._update_light_status_color(widget, 'red')
                    self.log_message("补光灯已关闭")
                else:
                    self.log_message(f"补光灯关闭失败: {response}")
                    
        except Exception as e:
            self.log_message(f"补光灯控制出错: {e}")

    def _update_light_status_color(self, widget, color):
        """递归更新补光灯状态标签颜色"""
        try:
            if isinstance(widget, ttk.Label) and hasattr(widget, 'cget'):
                try:
                    if widget.cget('textvariable') == str(self.light_status_var):
                        widget.config(foreground=color)
                except:
                    pass
            
            # 递归检查子组件
            for child in widget.winfo_children():
                self._update_light_status_color(child, color)
        except:
            pass

    def on_closing(self):
        """窗口关闭处理"""
        try:
            self.stop_system()
            self.stop_video()
            
            if hasattr(self, 'client') and self.client:
                self.client.disconnect()
                
            if hasattr(self, 'detector') and self.detector:
                self.detector.stop_detection()
                
        except Exception as e:
            logger.error(f"关闭时出错: {e}")
        finally:
            self.root.destroy()

    def run(self):
        """运行GUI"""
        try:
            self.root.mainloop()
        finally:
            self.on_closing()


def main(port='/dev/ttyUSB0'):
    """主函数"""
    root = tk.Tk()
    app = GarbageSorterGUI(root, port=port)
    app.run()


if __name__ == "__main__":
    main()