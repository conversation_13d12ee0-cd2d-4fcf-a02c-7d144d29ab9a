#!/usr/bin/env python3
"""
测试检测框清除功能
"""

import sys
import os
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_detection_clear():
    """测试检测结果清除功能"""
    print("🧪 测试检测框清除功能")
    print("=" * 40)
    
    try:
        from garbage_sorter.detection.rknn_detector import RKNNDetector
        
        # 创建检测器实例
        detector = RKNNDetector()
        
        # 检查是否有清除方法
        if hasattr(detector, 'clear_detection_results'):
            print("✅ 检测器有 clear_detection_results() 方法")
            
            # 模拟设置检测结果
            detector.last_detection = {
                'class_id': 1,
                'class_name': 'recyclable',
                'confidence': 0.85,
                'bbox': [100, 150, 140, 190]
            }
            
            print(f"设置检测结果: {detector.last_detection}")
            
            # 清除检测结果
            detector.clear_detection_results()
            
            if detector.last_detection is None:
                print("✅ 检测结果已成功清除")
                return True
            else:
                print("❌ 检测结果清除失败")
                return False
        else:
            print("❌ 检测器没有 clear_detection_results() 方法")
            return False
            
    except ImportError as e:
        print(f"❌ 导入检测器失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def test_gui_integration():
    """测试GUI集成"""
    print("\n🧪 测试GUI集成")
    print("=" * 30)
    
    try:
        # 检查GUI文件中是否包含清除调用
        gui_file = "src/garbage_sorter/gui/gui_client.py"
        
        if not os.path.exists(gui_file):
            print("❌ GUI文件不存在")
            return False
        
        with open(gui_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否在reset_detection_state中调用了clear_detection_results
        if 'clear_detection_results' in content:
            print("✅ GUI中包含检测结果清除调用")
            
            # 检查调用位置
            if 'reset_detection_state' in content and 'clear_detection_results' in content:
                print("✅ 在reset_detection_state方法中调用清除功能")
                return True
            else:
                print("⚠️ 清除调用可能不在正确位置")
                return False
        else:
            print("❌ GUI中没有检测结果清除调用")
            return False
            
    except Exception as e:
        print(f"❌ GUI集成测试异常: {e}")
        return False

def main():
    """主函数"""
    print("🚀 检测框清除功能测试")
    print("=" * 50)
    
    # 测试检测器清除功能
    detector_test = test_detection_clear()
    
    # 测试GUI集成
    gui_test = test_gui_integration()
    
    print("\n📊 测试结果:")
    print("-" * 30)
    print(f"检测器清除功能: {'✅ 通过' if detector_test else '❌ 失败'}")
    print(f"GUI集成测试: {'✅ 通过' if gui_test else '❌ 失败'}")
    
    overall_success = detector_test and gui_test
    print(f"总体结果: {'✅ 通过' if overall_success else '❌ 失败'}")
    
    if overall_success:
        print("\n🎯 修复说明:")
        print("1. ✅ 添加了 clear_detection_results() 方法到RKNN检测器")
        print("2. ✅ 在 reset_detection_state() 中调用清除方法")
        print("3. ✅ 分拣完成后检测框会被清除")
        print("4. ✅ 等待掉落状态下不显示检测框")
    else:
        print("\n❌ 需要检查修复实现")
    
    return overall_success

if __name__ == "__main__":
    main()
