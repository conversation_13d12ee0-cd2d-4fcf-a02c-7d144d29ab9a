/*
 * ========================================
 * Garbage Sorting System - Final Stable Version
 * ========================================
 *
 * Four Garbage Classification Types:
 * Command 1 - Recyclable (M4 reverse push)
 * Command 2 - Harmful    (M3 forward push)
 * Command 3 - Kitchen    (M4 forward push)
 * Command 4 - Other      (M3 reverse push)
 *
 * Hardware Configuration:
 * - M1 Stepper: EN-47, STEP-21, DIR-14 (Auxiliary)
 * - M2 Stepper: EN-13, STEP-12, DIR-11 (Conveyor)
 * - M3 DC Motor: IN1-10, IN2-9 + Limit Switch 2-GPIO37
 * - M4 DC Motor: IN1-46, IN2-3 + Limit Switch 1-GPIO38
 *
 * Brush Control Features:
 * - Startup time: 500ms
 * - Limit switch: Immediate brake
 * - Auto reset: Return to center
 * - Brake mode: Both pins high
 */

#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"
#include "driver/gpio.h"
#include "esp_log.h"
#include "esp_system.h"
#include "esp_vfs_dev.h"
#include "esp_vfs_usb_serial_jtag.h"
#include "driver/usb_serial_jtag.h"
#include "esp_rom_sys.h"  // 用于esp_rom_delay_us

// 引脚定义
// Stepper motorM1 (辅助机构)
#define STEPPER1_EN_PIN   47
#define STEPPER1_STEP_PIN 21
#define STEPPER1_DIR_PIN  14

// Stepper motorM2 (传送带)
#define STEPPER2_EN_PIN   13
#define STEPPER2_STEP_PIN 12
#define STEPPER2_DIR_PIN  11

// DC motorM3 (刷板1: 有害/Other)
#define DC_MOTOR3_IN1_PIN 10
#define DC_MOTOR3_IN2_PIN 9

// DC motorM4 (刷板2: 厨余/Recyclable)
#define DC_MOTOR4_IN1_PIN 46
#define DC_MOTOR4_IN2_PIN 3

// 限位开关
#define LIMIT_SWITCH1_PIN 38  // 限位开关1 (M4刷板2限位)
#define LIMIT_SWITCH2_PIN 37  // 限位开关2 (M3刷板1限位)

// 比赛启动按键
#define START_BUTTON_PIN 41   // 比赛启动按键（物理按键）- GPIO41

// 检测传感器
#define DROP_DETECT_PIN  16   // 掉落检测传感器IN1
#define FULL_DETECT_PIN  18   // 满载检测传感器IN3

// 补光灯
#define LIGHT_PIN        48   // 补光灯OUT1

// Garbage type definition
typedef enum {
    GARBAGE_RECYCLABLE = 1,  // Recyclable
    GARBAGE_HARMFUL = 2,     // Harmful
    GARBAGE_KITCHEN = 3,     // Kitchen
    GARBAGE_OTHER = 4        // Other
} garbage_type_t;

// 比赛模式定义
typedef enum {
    COMPETITION_MODE_NONE = 0,        // 无比赛模式（兼容旧版本）
    COMPETITION_MODE_PRELIMINARY = 1,  // 初赛模式：M1快速运行
    COMPETITION_MODE_FINAL = 2         // 决赛模式：M1标准运行+自动控制
} competition_mode_t;

static const char *TAG = "GARBAGE_SORTER";

// ========== 步进电机参数 ==========
#define STEPPER_STEPS_PER_REV 1600   // Stepper motor每转步数 (8步细分)
#define STEPPER_PULSE_WIDTH_US 50    // 步进脉冲宽度(微秒)
#define STEPPER_SPEED_DELAY_US 300   // M2传送带速度延时(微秒)
#define M1_SPEED_DELAY_US_SLOW 5000   // M1步进电机速度延时(微秒) - 决赛模式标准速度
#define M1_SPEED_DELAY_US_FAST 2000   // M1步进电机速度延时(微秒) - 初赛模式快速度

// ========== M2梯形加减速参数 ==========
#define STEPPER_MAX_SPEED     3000   // 最大速度 (步/秒)
#define STEPPER_MIN_SPEED     1000   // 最小Start速度 (步/秒) - 1000步/秒起步
#define STEPPER_ACCELERATION  1250   // 加速度 (步/秒²) - 调整为500步加速
#define ACCEL_STEPS           1000   // 加速段步数 - 1000步加速段
#define DECEL_STEPS           1000   // 减速段步数 - 1000步减速段

#define CONVEYOR_MOVE_TIME    2500   // Conveyor移动时间(ms)

// USB Serial JTAG参数
#define USB_SERIAL_JTAG_RX_BUFSIZE 64

// 梯形加减速状态结构体
typedef struct {
    float current_speed;       // 当前速度 (步/秒)
    float target_speed;        // 目标速度 (步/秒)
    uint32_t total_steps;      // 总步数
    uint32_t current_step;     // 当前步数
    uint32_t accel_steps;      // 加速段步数
    uint32_t decel_steps;      // 减速段步数
    uint32_t const_steps;      // 匀速段步数
    bool is_running;           // 是否正在Run
    bool direction;            // 运动方向
    uint32_t step_interval_us; // 当前步进间隔(微秒)
    bool continuous_mode;      // 持续Run模式
    uint32_t segment_step;     // 当前段内步数
} trapezoidal_profile_t;

// System state
typedef struct {
    bool processing;              // 是否正在Process垃圾
    garbage_type_t current_type;  // 当前垃圾Type
    bool conveyor_running;        // 普通Conveyor是否正在Run
    bool conveyor_direction;      // Conveyor方向：true=正转，false=反转
    bool trapezoidal_running;     // 梯形加速模式是否正在Run
    trapezoidal_profile_t stepper_profile; // Stepper motor梯形加减速状态

    // M1步进电机自动控制状态
    bool m1_auto_running;         // M1是否在自动运行模式
    bool m1_running;              // M1是否正在运行
    competition_mode_t competition_mode; // 比赛模式
    uint32_t m1_speed_delay_us;   // M1速度延时（根据比赛模式动态调整）
    
    // 比赛启动按键状态
    bool start_button_pressed;   // 按键是否被按下
    bool start_button_last_state; // 按键上一次状态（用于检测边沿）

    // 传感器状态
    bool drop_detected;          // 掉落检测状态
    bool drop_detected_last_state; // 掉落检测上一次状态（用于检测边沿）
    bool full_detected;          // 满载检测状态
    bool light_on;               // 补光灯状态
} system_state_t;

static system_state_t sys_state = {0};

// 函数声明
void init_gpio(void);
void init_usb_serial_jtag(void);
// ========== 系统Task函数 ==========
void usb_serial_jtag_task(void *pvParameters);
void conveyor_control_task(void *pvParameters);
void process_command(char *command);

// 删除不需要的M1步进电机控制

// M1步进电机自动控制函数
void start_m1_auto_running(void);                            // 启动M1自动运行模式
void stop_m1_auto_running(void);                             // 停止M1自动运行模式
void start_m1_preliminary_mode(void);                        // 启动M1初赛模式（快速）
void start_m1_final_mode(void);                              // 启动M1决赛模式（标准+自动控制）
void set_competition_mode(competition_mode_t mode);          // 设置比赛模式
void control_m1_motor(bool enable);                          // 控制M1电机启停
void m1_auto_control_task(void *pvParameters);               // M1自动控制任务

// M2传送带梯形加减速控制（精简版）
void update_continuous_trapezoidal_profile(void);
void start_conveyor_trapezoidal_run(void);                   // 简单的加速到匀速持续运行（正转）
void start_conveyor_trapezoidal_run_backward(void);          // 简单的加速到匀速持续运行（反转）
void stop_conveyor_trapezoidal(void);                        // Stop梯形加减速Run
void stop_conveyor(void);                                    // 停止传送带普通模式

// ========== 刷板控制模块 ==========
void move_brush(int motorA_pin, int motorB_pin, int limit_pin, bool direction, const char* motor_name);
void move_brush_m3(bool direction);  // M3刷板: true=Harmful, false=Other
void move_brush_m4(bool direction);  // M4刷板: true=Kitchen, false=Recyclable
void execute_anti_jam_procedure(const char* motor_name, bool original_direction);  // 防卡处理函数

// ========== 垃圾分类Process模块 ==========
void process_garbage(garbage_type_t type);
void stop_and_process_garbage(garbage_type_t type);

void app_main(void) {
    ESP_LOGI(TAG, "Garbage sorting system starting...");

    // 初始化系统状态
    memset(&sys_state, 0, sizeof(sys_state));
    
    // 设置默认比赛模式和速度参数
    sys_state.competition_mode = COMPETITION_MODE_NONE;
    sys_state.m1_speed_delay_us = M1_SPEED_DELAY_US_SLOW;  // 默认使用慢速

    // 初始化硬件
    init_gpio();
    init_usb_serial_jtag();

    ESP_LOGI(TAG, "System initialization complete");
    ESP_LOGI(TAG, "Garbage sorting system ready");
    ESP_LOGI(TAG, "步进电机M1: EN-%d, STEP-%d, DIR-%d (默认使能关闭)", STEPPER1_EN_PIN, STEPPER1_STEP_PIN, STEPPER1_DIR_PIN);
    ESP_LOGI(TAG, "传送带M2: EN-%d, STEP-%d, DIR-%d", STEPPER2_EN_PIN, STEPPER2_STEP_PIN, STEPPER2_DIR_PIN);
    ESP_LOGI(TAG, "刷板1 M3: IN1-%d, IN2-%d (有害/Other)", DC_MOTOR3_IN1_PIN, DC_MOTOR3_IN2_PIN);
    ESP_LOGI(TAG, "刷板2 M4: IN1-%d, IN2-%d (厨余/Recyclable)", DC_MOTOR4_IN1_PIN, DC_MOTOR4_IN2_PIN);
    ESP_LOGI(TAG, "");
    ESP_LOGI(TAG, "=== 垃圾分拣系统控制命令 ===");
    ESP_LOGI(TAG, "传送带控制:");
    ESP_LOGI(TAG, "  TRUN  - 正转运行(1000→3000步/秒)");
    ESP_LOGI(TAG, "  TRUNB - 反转运行(1000→3000步/秒)");
    ESP_LOGI(TAG, "  TS    - 停止运行");
    ESP_LOGI(TAG, "垃圾分类:");
    ESP_LOGI(TAG, "  1 - 可回收垃圾");
    ESP_LOGI(TAG, "  2 - 有害垃圾");
    ESP_LOGI(TAG, "  3 - 厨余垃圾");
    ESP_LOGI(TAG, "  4 - 其他垃圾");
    ESP_LOGI(TAG, "补光灯控制:");
    ESP_LOGI(TAG, "  LIGHT_ON / LON  - 开启补光灯");
    ESP_LOGI(TAG, "  LIGHT_OFF / LOFF - 关闭补光灯");
    ESP_LOGI(TAG, "  LT - 切换补光灯状态");
    ESP_LOGI(TAG, "M1电机控制:");
    ESP_LOGI(TAG, "  M1ON   - 启动M1电机自动运行（兼容模式）");
    ESP_LOGI(TAG, "  M1OFF  - 停止M1电机自动运行");
    ESP_LOGI(TAG, "  M1FAST - 启动M1初赛模式（快速运行）");
    ESP_LOGI(TAG, "  M1SLOW - 启动M1决赛模式（标准速度+自动控制）");
    ESP_LOGI(TAG, "  M1RESTART - 决赛模式下重启M1（分拣完成后使用）");
    ESP_LOGI(TAG, "系统状态:");
    ESP_LOGI(TAG, "  STATUS - 查询系统状态");

    // CreateUSB Serial JTAGProcessTask
    ESP_LOGI(TAG, "CreateUSB Serial JTAGTask...");
    BaseType_t result = xTaskCreate(usb_serial_jtag_task, "usb_serial_jtag_task", 6144, NULL, 5, NULL);
    if (result == pdPASS) {
        ESP_LOGI(TAG, "USB Serial JTAGTaskCreateSuccess");
    } else {
        ESP_LOGE(TAG, "USB Serial JTAGTaskCreateFailed");
    }

    // Create传送带控制Task
    ESP_LOGI(TAG, "Create传送带控制Task...");
    result = xTaskCreate(conveyor_control_task, "conveyor_control_task", 8192, NULL, 4, NULL);
    if (result == pdPASS) {
        ESP_LOGI(TAG, "传送带控制TaskCreateSuccess");
    } else {
        ESP_LOGE(TAG, "传送带控制TaskCreateFailed");
    }

    // CreateM1自动控制Task
    ESP_LOGI(TAG, "CreateM1自动控制Task...");
    result = xTaskCreate(m1_auto_control_task, "m1_auto_control_task", 4096, NULL, 3, NULL);
    if (result == pdPASS) {
        ESP_LOGI(TAG, "M1自动控制TaskCreateSuccess");
    } else {
        ESP_LOGE(TAG, "M1自动控制TaskCreateFailed");
    }

    // M1默认不转动，等待GPIO41按钮触发
    ESP_LOGI(TAG, "M1电机默认关闭，等待GPIO41按钮触发启动");

    // 主循环
    while (1) {
        vTaskDelay(pdMS_TO_TICKS(1000));
    }
}

void init_gpio(void) {
    gpio_config_t io_conf = {};

    // 配置步进电机和直流电机输出引脚
    io_conf.intr_type = GPIO_INTR_DISABLE;
    io_conf.mode = GPIO_MODE_OUTPUT;
    io_conf.pin_bit_mask = (1ULL << STEPPER1_EN_PIN) | (1ULL << STEPPER1_STEP_PIN) |
                          (1ULL << STEPPER1_DIR_PIN) | (1ULL << STEPPER2_EN_PIN) |
                          (1ULL << STEPPER2_STEP_PIN) | (1ULL << STEPPER2_DIR_PIN) |
                          (1ULL << DC_MOTOR3_IN1_PIN) | (1ULL << DC_MOTOR3_IN2_PIN) |
                          (1ULL << DC_MOTOR4_IN1_PIN) | (1ULL << DC_MOTOR4_IN2_PIN);
    io_conf.pull_down_en = 0;
    io_conf.pull_up_en = 0;
    gpio_config(&io_conf);

    // 配置输入引脚（限位开关、启动按键、传感器）
    io_conf.mode = GPIO_MODE_INPUT;
    io_conf.pin_bit_mask = (1ULL << LIMIT_SWITCH1_PIN) | (1ULL << LIMIT_SWITCH2_PIN) |
                          (1ULL << START_BUTTON_PIN) | (1ULL << DROP_DETECT_PIN) |
                          (1ULL << FULL_DETECT_PIN);
    io_conf.pull_up_en = 1;  // 启用上拉电阻
    gpio_config(&io_conf);

    // 配置补光灯输出引脚
    io_conf.mode = GPIO_MODE_OUTPUT;
    io_conf.pin_bit_mask = (1ULL << LIGHT_PIN);
    io_conf.pull_up_en = 0;
    io_conf.pull_down_en = 0;
    gpio_config(&io_conf);

    // 初始化步进电机状态
    gpio_set_level(STEPPER1_EN_PIN, 1);  // M1使能默认关闭（高电平禁用）
    gpio_set_level(STEPPER1_STEP_PIN, 0);
    gpio_set_level(STEPPER1_DIR_PIN, 0);

    gpio_set_level(STEPPER2_EN_PIN, 1);  // M2高电平禁用
    gpio_set_level(STEPPER2_STEP_PIN, 0);
    gpio_set_level(STEPPER2_DIR_PIN, 0);

    // 初始化直流电机为刹车状态 (两个引脚都拉高)
    gpio_set_level(DC_MOTOR3_IN1_PIN, 1);
    gpio_set_level(DC_MOTOR3_IN2_PIN, 1);
    gpio_set_level(DC_MOTOR4_IN1_PIN, 1);
    gpio_set_level(DC_MOTOR4_IN2_PIN, 1);

    // 初始化按键和传感器状态
    sys_state.start_button_pressed = false;
    sys_state.start_button_last_state = gpio_get_level(START_BUTTON_PIN);
    sys_state.drop_detected = false;
    sys_state.full_detected = false;
    sys_state.light_on = false;

    // 初始化补光灯为关闭状态
    gpio_set_level(LIGHT_PIN, 0);

    ESP_LOGI(TAG, "GPIO初始化Complete");
}

/**
 * 检测启动按键是否被按下（下降沿检测）
 * @return true 如果按键被按下
 */
bool check_start_button(void) {
    bool current_state = gpio_get_level(START_BUTTON_PIN);
    bool button_pressed = false;

    // 检测下降沿（按键按下）
    if (sys_state.start_button_last_state == 1 && current_state == 0) {
        button_pressed = true;
        sys_state.start_button_pressed = true;
        ESP_LOGI(TAG, "🚀 比赛启动按键被按下！");
    }

    sys_state.start_button_last_state = current_state;
    return button_pressed;
}

/**
 * 检测掉落传感器状态
 * @return true 如果检测到垃圾掉落
 */
bool check_drop_sensor(void) {
    bool current_state = gpio_get_level(DROP_DETECT_PIN);

    // 假设传感器低电平表示检测到物体
    if (current_state == 0 && !sys_state.drop_detected) {
        sys_state.drop_detected = true;
        ESP_LOGI(TAG, "📦 检测到垃圾掉落");
        return true;
    } else if (current_state == 1 && sys_state.drop_detected) {
        sys_state.drop_detected = false;
        ESP_LOGI(TAG, "📦 垃圾掉落检测复位");
    }

    return false;
}

/**
 * 检测满载传感器状态
 * @return true 如果检测到满载
 */
bool check_full_sensor(void) {
    bool current_state = gpio_get_level(FULL_DETECT_PIN);

    // 假设传感器低电平表示满载
    if (current_state == 0 && !sys_state.full_detected) {
        sys_state.full_detected = true;
        ESP_LOGI(TAG, "🗑️ 检测到垃圾桶满载");
        return true;
    } else if (current_state == 1 && sys_state.full_detected) {
        sys_state.full_detected = false;
        ESP_LOGI(TAG, "🗑️ 满载检测复位");
    }

    return false;
}

/**
 * 控制补光灯
 * @param on true=开启, false=关闭
 */
void control_light(bool on) {
    gpio_set_level(LIGHT_PIN, on ? 1 : 0);
    sys_state.light_on = on;
    ESP_LOGI(TAG, "💡 补光灯%s", on ? "开启" : "关闭");
}

void init_usb_serial_jtag(void) {
    ESP_LOGI(TAG, "初始化USB Serial JTAG...");

    // 配置USB Serial JTAG
    usb_serial_jtag_driver_config_t usb_serial_jtag_config = {
        .rx_buffer_size = 1024,
        .tx_buffer_size = 1024,
    };

    // 安装USB Serial JTAG驱动
    ESP_ERROR_CHECK(usb_serial_jtag_driver_install(&usb_serial_jtag_config));

    ESP_LOGI(TAG, "USB Serial JTAG初始化Complete");
}

// ========================================
// Stepper motor控制模块
// ========================================

// M1步进电机控制函数
void control_stepper_m1(bool direction, int duration_ms) {
    // M1已经在初始化时启用（使能调到最低）
    ESP_LOGI(TAG, "M1步进电机%sRun %dms", direction ? "正转" : "反转", duration_ms);

    // 设置方向
    gpio_set_level(STEPPER1_DIR_PIN, direction ? 1 : 0);
    vTaskDelay(pdMS_TO_TICKS(5));

    // 按时间发送步进脉冲
    uint32_t start_time = xTaskGetTickCount();
    uint32_t end_time = start_time + pdMS_TO_TICKS(duration_ms);

    while (xTaskGetTickCount() < end_time) {
        gpio_set_level(STEPPER1_STEP_PIN, 1);
        esp_rom_delay_us(STEPPER_PULSE_WIDTH_US);
        gpio_set_level(STEPPER1_STEP_PIN, 0);
        esp_rom_delay_us(STEPPER_SPEED_DELAY_US);
    }

    ESP_LOGI(TAG, "M1步进电机RunComplete");
}

// ========================================
// M2传送带梯形加减速控制函数
// ========================================

// 删除不需要的定步数初始化函数

/**
 * 初始化持续运行的梯形加减速配置（基于目标速度）
 * @param direction 方向 (true=正转, false=反转)
 * @param target_speed 目标速度 (步/秒)
 */
void init_continuous_trapezoidal_profile(bool direction, float target_speed) {
    trapezoidal_profile_t *profile = &sys_state.stepper_profile;

    profile->total_steps = 0;  // 持续运行模式不限制总步数
    profile->current_step = 0;
    profile->direction = direction;
    profile->current_speed = STEPPER_MIN_SPEED;
    profile->target_speed = target_speed;
    profile->is_running = true;
    profile->continuous_mode = true;

    // 使用固定的加速段步数
    profile->accel_steps = ACCEL_STEPS;  // 1000步加速段
    profile->decel_steps = DECEL_STEPS;  // 1000步减速段
    profile->const_steps = 0;  // 持续运行模式匀速段无限制

    // 计算初始步进间隔
    profile->step_interval_us = (uint32_t)(1000000.0f / profile->current_speed);
    profile->segment_step = 0;

    ESP_LOGI(TAG, "持续梯形加减速初始化: 目标速度=%.1f步/秒, 加速段=%lu步",
             target_speed, profile->accel_steps);
}

/**
 * 更新梯形加减速状态
 */
void update_trapezoidal_profile(void) {
    trapezoidal_profile_t *profile = &sys_state.stepper_profile;

    if (!profile->is_running) return;

    // 判断当前处于哪个阶段
    if (profile->current_step < profile->accel_steps) {
        // 加速阶段
        float progress = (float)profile->current_step / profile->accel_steps;
        profile->current_speed = STEPPER_MIN_SPEED +
                               (STEPPER_MAX_SPEED - STEPPER_MIN_SPEED) * progress;
    } else if (profile->current_step < profile->accel_steps + profile->const_steps) {
        // 匀速阶段
        profile->current_speed = STEPPER_MAX_SPEED;
    } else {
        // 减速阶段
        uint32_t decel_step = profile->current_step - profile->accel_steps - profile->const_steps;
        float progress = (float)decel_step / profile->decel_steps;
        profile->current_speed = STEPPER_MAX_SPEED -
                               (STEPPER_MAX_SPEED - STEPPER_MIN_SPEED) * progress;
    }

    // 更新步进间隔
    profile->step_interval_us = (uint32_t)(1000000.0f / profile->current_speed);

    // 限制最小间隔，防止过快
    if (profile->step_interval_us < 100) {
        profile->step_interval_us = 100;
    }
}

/**
 * 检查梯形加减速是否Complete
 */
bool is_trapezoidal_complete(void) {
    trapezoidal_profile_t *profile = &sys_state.stepper_profile;
    return !profile->is_running || (profile->current_step >= profile->total_steps);
}


/**
 * 持续Run的梯形加减速更新函数 - Start时加速到最高速度后保持匀速
 */
void update_continuous_trapezoidal_profile(void) {
    trapezoidal_profile_t *profile = &sys_state.stepper_profile;

    // 简化状态检查
    if (!profile->is_running || !profile->continuous_mode) {
        return;
    }

    // 简单的两阶段控制：加速阶段 + 匀速阶段
    if (profile->segment_step < profile->accel_steps) {
        // 加速阶段：线性加速从起始速度到目标速度
        float progress = (float)profile->segment_step / profile->accel_steps;
        profile->current_speed = STEPPER_MIN_SPEED +
                               (profile->target_speed - STEPPER_MIN_SPEED) * progress;

        // 每500步输出一次状态，减少栈使用
        if (profile->segment_step % 500 == 0) {
            ESP_LOGI(TAG, "加速中: %lu/%lu步, 速度=%.1f步/秒",
                     profile->segment_step, profile->accel_steps, profile->current_speed);
        }
    } else {
        // 匀速阶段：保持目标速度
        profile->current_speed = profile->target_speed;

        // 只在刚进入匀速时输出一次
        if (profile->segment_step == profile->accel_steps) {
            ESP_LOGI(TAG, "加速完成！进入匀速运行: %.1f步/秒", profile->current_speed);
        }
    }

    // 计算步进间隔
    profile->step_interval_us = (uint32_t)(1000000.0f / profile->current_speed);

    // 限制最小间隔
    if (profile->step_interval_us < 100) {
        profile->step_interval_us = 100;
    }

    // 步数计数递增
    profile->segment_step++;
}

/**
 * Start持续梯形加减速Run
 * @param direction 方向 (true=正转, false=反转)
 */
void start_conveyor_trapezoidal_continuous(bool direction) {
    ESP_LOGI(TAG, "Start传送带M2持续梯形加减速%s", direction ? "正转" : "反转");

    // 检查是否有其他模式正在运行
    if (sys_state.conveyor_running) {
        ESP_LOGW(TAG, "普通传送带模式正在运行，先停止");
        stop_conveyor();
        vTaskDelay(pdMS_TO_TICKS(100));
    }

    if (sys_state.trapezoidal_running) {
        ESP_LOGW(TAG, "梯形加速模式已在运行，先停止");
        stop_conveyor_trapezoidal();
        vTaskDelay(pdMS_TO_TICKS(100));
    }

    trapezoidal_profile_t *profile = &sys_state.stepper_profile;

    // 启用步进电机
    gpio_set_level(STEPPER2_EN_PIN, 0);  // 低电平启用
    vTaskDelay(pdMS_TO_TICKS(500));

    // 设置方向
    gpio_set_level(STEPPER2_DIR_PIN, direction ? 1 : 0);
    vTaskDelay(pdMS_TO_TICKS(5));

    // 初始化持续Run配置
    profile->direction = direction;
    profile->current_speed = STEPPER_MIN_SPEED;
    profile->target_speed = STEPPER_MAX_SPEED;
    profile->is_running = true;
    profile->continuous_mode = true;
    profile->segment_step = 0;
    profile->step_interval_us = (uint32_t)(1000000.0f / profile->current_speed);

    // 更新系统状态 - 梯形加速模式使用独立标志
    sys_state.trapezoidal_running = true;
    sys_state.conveyor_direction = direction;

    // ESP_LOGI(TAG, "Conveyor M2 continuous trapezoidal started");
    // ESP_LOGI(TAG, "Initial speed: %.1f steps/sec, interval: %lu us",
    //          profile->current_speed, profile->step_interval_us);
    // ESP_LOGI(TAG, "Acceleration steps: %d, Max speed: %d", ACCEL_STEPS, STEPPER_MAX_SPEED);
    // ESP_LOGI(TAG, "Status: trapezoidal_running=%d, is_running=%d, continuous_mode=%d",
    //          sys_state.trapezoidal_running, profile->is_running, profile->continuous_mode);
}

/**
 * 简单的梯形加速到匀速持续运行
 * 按照T3200的逻辑：加速1000步到3000步/秒，然后持续匀速运行
 */
void start_conveyor_trapezoidal_run(void) {
    ESP_LOGI(TAG, "Start简单梯形加速到匀速持续运行");

    // 检查是否有其他模式正在运行
    if (sys_state.conveyor_running) {
        ESP_LOGW(TAG, "普通传送带模式正在运行，先停止");
        stop_conveyor();
        vTaskDelay(pdMS_TO_TICKS(100));
    }

    if (sys_state.trapezoidal_running) {
        ESP_LOGW(TAG, "梯形加速模式已在运行，先停止");
        stop_conveyor_trapezoidal();
        vTaskDelay(pdMS_TO_TICKS(100));
    }

    trapezoidal_profile_t *profile = &sys_state.stepper_profile;

    // 启用步进电机
    gpio_set_level(STEPPER2_EN_PIN, 0);  // 低电平启用
    vTaskDelay(pdMS_TO_TICKS(500));

    // 设置方向（默认正转）
    gpio_set_level(STEPPER2_DIR_PIN, 1);
    vTaskDelay(pdMS_TO_TICKS(5));

    // 初始化简单运行配置 - 按照T3200的参数
    profile->direction = true;
    profile->current_speed = STEPPER_MIN_SPEED;      // 1000步/秒起始
    profile->target_speed = STEPPER_MAX_SPEED;       // 3000步/秒目标
    profile->is_running = true;
    profile->continuous_mode = true;
    profile->segment_step = 0;
    profile->accel_steps = ACCEL_STEPS;              // 1000步加速段
    profile->step_interval_us = (uint32_t)(1000000.0f / profile->current_speed);

    // 更新系统状态
    sys_state.trapezoidal_running = true;
    sys_state.conveyor_direction = true;

    ESP_LOGI(TAG, "简单梯形加速运行已启动");
    ESP_LOGI(TAG, "起始速度: %.1f步/秒, 目标速度: %.1f步/秒, 加速段: %lu步",
             profile->current_speed, profile->target_speed, profile->accel_steps);
}

/**
 * 简单的梯形加速到匀速持续运行（反转）
 * 按照T3200的逻辑：加速1000步到3000步/秒，然后持续匀速反转运行
 */
void start_conveyor_trapezoidal_run_backward(void) {
    ESP_LOGI(TAG, "Start简单梯形加速到匀速持续运行（反转）");

    // 检查是否有其他模式正在运行
    if (sys_state.conveyor_running) {
        ESP_LOGW(TAG, "普通传送带模式正在运行，先停止");
        stop_conveyor();
        vTaskDelay(pdMS_TO_TICKS(100));
    }

    if (sys_state.trapezoidal_running) {
        ESP_LOGW(TAG, "梯形加速模式已在运行，先停止");
        stop_conveyor_trapezoidal();
        vTaskDelay(pdMS_TO_TICKS(100));
    }

    trapezoidal_profile_t *profile = &sys_state.stepper_profile;

    // 启用步进电机
    gpio_set_level(STEPPER2_EN_PIN, 0);  // 低电平启用
    vTaskDelay(pdMS_TO_TICKS(500));

    // 设置方向（反转）
    gpio_set_level(STEPPER2_DIR_PIN, 0);  // 0为反转
    vTaskDelay(pdMS_TO_TICKS(5));

    // 初始化简单运行配置 - 按照T3200的参数
    profile->direction = false;                          // 反转
    profile->current_speed = STEPPER_MIN_SPEED;          // 1000步/秒起始
    profile->target_speed = STEPPER_MAX_SPEED;           // 3000步/秒目标
    profile->is_running = true;
    profile->continuous_mode = true;
    profile->segment_step = 0;
    profile->accel_steps = ACCEL_STEPS;                  // 1000步加速段
    profile->step_interval_us = (uint32_t)(1000000.0f / profile->current_speed);

    // 更新系统状态
    sys_state.trapezoidal_running = true;
    sys_state.conveyor_direction = false;               // 反转方向

    ESP_LOGI(TAG, "简单梯形加速反转运行已启动");
    ESP_LOGI(TAG, "起始速度: %.1f步/秒, 目标速度: %.1f步/秒, 加速段: %lu步",
             profile->current_speed, profile->target_speed, profile->accel_steps);
}

/**
 * Start定速度持续梯形加减速Run
 * @param direction 方向 (true=正转, false=反转)
 * @param target_speed 目标速度 (步/秒)
 */
void start_conveyor_trapezoidal_speed(bool direction, float target_speed) {
    ESP_LOGI(TAG, "Start传送带M2定速度梯形加减速%s，目标速度: %.1f步/秒",
             direction ? "正转" : "反转", target_speed);

    // 检查是否有其他模式正在运行
    if (sys_state.conveyor_running) {
        ESP_LOGW(TAG, "普通传送带模式正在运行，先停止");
        stop_conveyor();
        vTaskDelay(pdMS_TO_TICKS(100));
    }

    if (sys_state.trapezoidal_running) {
        ESP_LOGW(TAG, "梯形加速模式已在运行，先停止");
        stop_conveyor_trapezoidal();
        vTaskDelay(pdMS_TO_TICKS(100));
    }

    trapezoidal_profile_t *profile = &sys_state.stepper_profile;

    // 启用步进电机
    gpio_set_level(STEPPER2_EN_PIN, 0);  // 低电平启用
    vTaskDelay(pdMS_TO_TICKS(500));

    // 设置方向
    gpio_set_level(STEPPER2_DIR_PIN, direction ? 1 : 0);
    vTaskDelay(pdMS_TO_TICKS(5));

    // 初始化定速度持续Run配置
    init_continuous_trapezoidal_profile(direction, target_speed);

    // 更新系统状态 - 梯形加速模式使用独立标志
    sys_state.trapezoidal_running = true;
    sys_state.conveyor_direction = direction;

    ESP_LOGI(TAG, "Conveyor M2 speed-based continuous trapezoidal started");
    ESP_LOGI(TAG, "Initial speed: %.1f steps/sec, target: %.1f steps/sec, interval: %lu us",
             profile->current_speed, profile->target_speed, profile->step_interval_us);
    ESP_LOGI(TAG, "Acceleration steps: %d, Target speed: %.1f", ACCEL_STEPS, target_speed);
    ESP_LOGI(TAG, "Status: trapezoidal_running=%d, is_running=%d, continuous_mode=%d",
             sys_state.trapezoidal_running, profile->is_running, profile->continuous_mode);
}

/**
 * Stop梯形加减速Run - 平滑减速Stop
 */
void stop_conveyor_trapezoidal(void) {
    ESP_LOGI(TAG, "Start平滑减速Stop conveyorM2");

    trapezoidal_profile_t *profile = &sys_state.stepper_profile;

    if (!sys_state.trapezoidal_running || !profile->is_running || !profile->continuous_mode) {
        ESP_LOGI(TAG, "传送带未在梯形加减速模式Run");
        return;
    }

    // 执行减速Stop过程
    uint32_t decel_steps = DECEL_STEPS;  // 1000步减速段
    float initial_speed = profile->current_speed;

    ESP_LOGI(TAG, "Start deceleration: current speed=%.1f steps/sec", initial_speed);

    for (uint32_t i = 0; i < decel_steps; i++) {
        // 计算减速过程中的速度 - 从当前速度减速到最小速度
        float progress = (float)i / decel_steps;
        profile->current_speed = initial_speed - (initial_speed - STEPPER_MIN_SPEED) * progress;

        // 更新步进间隔
        profile->step_interval_us = (uint32_t)(1000000.0f / profile->current_speed);
        if (profile->step_interval_us < 100) {
            profile->step_interval_us = 100;
        }

        // 发送步进脉冲
        gpio_set_level(STEPPER2_STEP_PIN, 1);
        esp_rom_delay_us(STEPPER_PULSE_WIDTH_US);
        gpio_set_level(STEPPER2_STEP_PIN, 0);
        esp_rom_delay_us(profile->step_interval_us - STEPPER_PULSE_WIDTH_US);

        // 每50步输出一次状态
        if (i % 50 == 0) {
            ESP_LOGI(TAG, "Decelerating: step=%lu/%lu, speed=%.1f steps/sec", i, decel_steps, profile->current_speed);
        }
    }

    // StopRun
    profile->is_running = false;
    profile->continuous_mode = false;

    // 禁用步进电机
    gpio_set_level(STEPPER2_EN_PIN, 1);  // 高电平禁用

    // 更新系统状态 - 梯形加速模式使用独立标志
    sys_state.trapezoidal_running = false;

    ESP_LOGI(TAG, "传送带M2已平滑减速Stop");
}

/**
 * 停止传送带普通模式运行
 */
void stop_conveyor(void) {
    ESP_LOGI(TAG, "停止传送带普通模式运行");
    
    // 更新系统状态
    sys_state.conveyor_running = false;
    
    // 禁用步进电机
    gpio_set_level(STEPPER2_EN_PIN, 1);  // 高电平禁用
    gpio_set_level(STEPPER2_STEP_PIN, 0);
    
    ESP_LOGI(TAG, "传送带已停止");
}

// ========================================
// Brush控制模块 - 四种垃圾分类
// ========================================

/**
 * 防卡处理函数
 * @param motor_name 电机名称
 * @param original_direction 原始运动方向
 */
void execute_anti_jam_procedure(const char* motor_name, bool original_direction) {
    ESP_LOGW(TAG, "=== %s刷板防卡处理开始 ===", motor_name);

    // 确定刷板类型和对应的引脚
    int motorA_pin, motorB_pin, limit_pin;
    bool is_m3_motor = (strcmp(motor_name, "M3") == 0);

    if (is_m3_motor) {
        motorA_pin = DC_MOTOR3_IN1_PIN;
        motorB_pin = DC_MOTOR3_IN2_PIN;
        limit_pin = LIMIT_SWITCH2_PIN;
    } else {  // M4
        motorA_pin = DC_MOTOR4_IN1_PIN;
        motorB_pin = DC_MOTOR4_IN2_PIN;
        limit_pin = LIMIT_SWITCH1_PIN;
    }

    // 第一步：刷板反向运动到限位位置，完全脱离卡住区域
    ESP_LOGI(TAG, "%s刷板反向运动到限位位置，脱离卡住区域", motor_name);
    gpio_set_level(motorA_pin, original_direction ? 0 : 1);  // 反向
    gpio_set_level(motorB_pin, original_direction ? 1 : 0);
    vTaskDelay(pdMS_TO_TICKS(50));  // 启动稳定时间

    // 等待反向运动到限位位置（最多2秒）
    int reverse_timeout = 0;
    const int REVERSE_MAX_TIMEOUT = 2000;
    while(gpio_get_level(limit_pin) == 1 && reverse_timeout < REVERSE_MAX_TIMEOUT) {
        vTaskDelay(pdMS_TO_TICKS(1));
        reverse_timeout++;
    }

    // 刹车停止
    gpio_set_level(motorA_pin, 1);
    gpio_set_level(motorB_pin, 1);

    if (reverse_timeout >= REVERSE_MAX_TIMEOUT) {
        ESP_LOGW(TAG, "%s刷板反向运动超时，可能机械故障", motor_name);
    } else {
        ESP_LOGI(TAG, "%s刷板已反向到达限位位置", motor_name);
    }

    // 第二步：启动传送带辅助清理
    ESP_LOGI(TAG, "启动传送带辅助清理异物");
    // 修复：根据刷板所在区域决定传送带方向，而不是刷板运动方向
    if (is_m3_motor) {
        // M3刷板负责左侧区域（有害、其他垃圾），传送带向左运动
        ESP_LOGI(TAG, "M3刷板防卡 - 传送带向左侧运动清理异物");
        start_conveyor_trapezoidal_run_backward();  // 反转向左
    } else {
        // M4刷板负责右侧区域（可回收、厨余垃圾），传送带向右运动
        ESP_LOGI(TAG, "M4刷板防卡 - 传送带向右侧运动清理异物");
        start_conveyor_trapezoidal_run();  // 正转向右
    }

    // 第三步：传送带运行1秒，帮助清理异物
    vTaskDelay(pdMS_TO_TICKS(1000));

    // 第四步：停止传送带
    stop_conveyor_trapezoidal();
    ESP_LOGI(TAG, "传送带辅助清理完成");

    // 第五步：刷板重新正向移动，尝试正常分拣动作
    ESP_LOGI(TAG, "%s刷板重新正向移动，尝试正常分拣", motor_name);

    // 启动刷板正向运动（原始方向）
    gpio_set_level(motorA_pin, original_direction ? 1 : 0);  // 原始方向
    gpio_set_level(motorB_pin, original_direction ? 0 : 1);
    vTaskDelay(pdMS_TO_TICKS(50));  // 启动稳定时间

    // 等待正向运动到限位位置（最多2秒）
    int forward_timeout = 0;
    const int FORWARD_MAX_TIMEOUT = 2000;
    while(gpio_get_level(limit_pin) == 1 && forward_timeout < FORWARD_MAX_TIMEOUT) {
        vTaskDelay(pdMS_TO_TICKS(1));
        forward_timeout++;
    }

    // 刹车
    gpio_set_level(motorA_pin, 1);
    gpio_set_level(motorB_pin, 1);

    if (forward_timeout >= FORWARD_MAX_TIMEOUT) {
        ESP_LOGE(TAG, "%s刷板防卡后正向运动仍超时", motor_name);
    } else {
        ESP_LOGI(TAG, "%s刷板防卡后正向运动成功", motor_name);
    }

    // 第六步：等待系统稳定
    vTaskDelay(pdMS_TO_TICKS(500));

    ESP_LOGW(TAG, "=== %s刷板防卡处理完成 ===", motor_name);
}

// M3刷板控制 (有害/Other)
void move_brush_m3(bool direction) {
    const char* garbage_type = direction ? "Harmful" : "Other";
    ESP_LOGI(TAG, "M3Push%s", garbage_type);

    // 使用Arduino风格刷板移动函数
    move_brush(DC_MOTOR3_IN1_PIN, DC_MOTOR3_IN2_PIN, LIMIT_SWITCH2_PIN, direction, "M3");
}

// M4刷板控制 (厨余/Recyclable) - 使用Arduino风格函数
void move_brush_m4(bool direction) {
    const char* garbage_type = direction ? "Kitchen" : "Recyclable";
    ESP_LOGI(TAG, "M4Push%s", garbage_type);

    // 使用Arduino风格刷板移动函数
    move_brush(DC_MOTOR4_IN1_PIN, DC_MOTOR4_IN2_PIN, LIMIT_SWITCH1_PIN, direction, "M4");
}

/**
 * 刷板通用移动函数 - 带防卡功能版本
 * @param motorA_pin 电机控制引脚A
 * @param motorB_pin 电机控制引脚B
 * @param limit_pin 限位开关引脚
 * @param direction 方向：true=正向（A高B低），false=反向（A低B高）
 * @param motor_name 电机名称（用于日志）
 */
void move_brush(int motorA_pin, int motorB_pin, int limit_pin, bool direction, const char* motor_name) {
    ESP_LOGI(TAG, "%s刷板%s移动", motor_name, direction ? "正向" : "反向");

    // 最多尝试2次（正常执行 + 防卡重试）
    for (int attempt = 1; attempt <= 2; attempt++) {
        ESP_LOGI(TAG, "%s刷板第%d次尝试", motor_name, attempt);

        // 第一阶段：Push到限位位置（带防卡检测）
        gpio_set_level(motorA_pin, direction ? 1 : 0);
        gpio_set_level(motorB_pin, direction ? 0 : 1);
        vTaskDelay(pdMS_TO_TICKS(500));  // Start稳定时间：500ms

        // 防卡检测：最多等待2秒
        int timeout_count = 0;
        const int MAX_TIMEOUT = 2000;  // 2秒超时
        bool limit_reached = false;

        while(gpio_get_level(limit_pin) == 1 && timeout_count < MAX_TIMEOUT) {
            vTaskDelay(pdMS_TO_TICKS(1));
            timeout_count++;
        }

        limit_reached = (gpio_get_level(limit_pin) == 0);

        if (limit_reached) {
            // 正常到达限位
            gpio_set_level(motorA_pin, 1);
            gpio_set_level(motorB_pin, 1);
            ESP_LOGI(TAG, "%s刷板正常到达限位，立即刹车", motor_name);
            break;  // 成功，跳出重试循环
        } else {
            // 超时，触发防卡功能
            ESP_LOGW(TAG, "%s刷板运行超时(%dms)，触发防卡功能", motor_name, timeout_count);

            // 立即刹车
            gpio_set_level(motorA_pin, 1);
            gpio_set_level(motorB_pin, 1);
            vTaskDelay(pdMS_TO_TICKS(100));  // 刹车稳定时间

            // 防卡处理：刷板反向运动 + 传送带辅助 + 重新正向尝试
            execute_anti_jam_procedure(motor_name, direction);

            if (attempt == 2) {
                ESP_LOGE(TAG, "%s刷板防卡处理后仍然失败，放弃操作", motor_name);
                sys_state.processing = false;  // 清除处理状态，避免系统永远busy
                return;  // 两次尝试都失败，放弃
            }

            ESP_LOGI(TAG, "%s刷板防卡处理完成，第%d次尝试结束", motor_name, attempt);
            // 防卡处理已包含完整动作，直接跳出循环，不需要额外回程
            goto anti_jam_complete;
        }
    }

    // 第二阶段：向反方向移动（回到中间位置）
    gpio_set_level(motorA_pin, direction ? 0 : 1);  // 反向
    gpio_set_level(motorB_pin, direction ? 1 : 0);
    vTaskDelay(pdMS_TO_TICKS(50));  // Start稳定时间：50ms

    // 等待回到中间位置（也有超时保护）
    int return_timeout = 0;
    const int RETURN_MAX_TIMEOUT = 3000;  // 回程最多3秒

    while(gpio_get_level(limit_pin) == 1 && return_timeout < RETURN_MAX_TIMEOUT) {
        vTaskDelay(pdMS_TO_TICKS(1));
        return_timeout++;
    }

    // 立马执行刹车 - Complete复位
    gpio_set_level(motorA_pin, 1);
    gpio_set_level(motorB_pin, 1);

    if (return_timeout >= RETURN_MAX_TIMEOUT) {
        ESP_LOGE(TAG, "%s刷板回程超时，可能存在机械故障", motor_name);
        sys_state.processing = false;  // 超时时清除处理状态
    } else {
        ESP_LOGI(TAG, "%s刷板复位Complete", motor_name);
    }

anti_jam_complete:
    ESP_LOGI(TAG, "%s刷板操作完成", motor_name);
}





// ========================================
// Garbage classificationProcess模块 - 四种垃圾Type
// ========================================

// Garbage classificationProcess主函数 - 只执行刷板操作
void process_garbage(garbage_type_t type) {
    if (sys_state.processing) {
        ESP_LOGW(TAG, "系统Busy，请稍后再试");
        return;
    }

    sys_state.processing = true;
    sys_state.current_type = type;

    ESP_LOGI(TAG, "=== StartProcess垃圾Type: %d ===", type);

    switch (type) {
        case GARBAGE_RECYCLABLE:  // Recyclable
            ESP_LOGI(TAG, "ProcessRecyclable");
            move_brush_m4(false);                      // M4反转推Recyclable（自动返回中间位置）
            break;

        case GARBAGE_HARMFUL:     // Harmful
            ESP_LOGI(TAG, "ProcessHarmful");
            move_brush_m3(true);                       // M3前进推Harmful（自动返回中间位置）
            break;

        case GARBAGE_KITCHEN:     // Kitchen
            ESP_LOGI(TAG, "ProcessKitchen");
            move_brush_m4(true);                       // M4前进推Kitchen（自动返回中间位置）
            break;

        case GARBAGE_OTHER:       // Other
            ESP_LOGI(TAG, "ProcessOther");
            move_brush_m3(false);                      // M3反转推Other（自动返回中间位置）
            break;

        default:
            ESP_LOGE(TAG, "Invalid的垃圾Type: %d", type);
            break;
    }

    ESP_LOGI(TAG, "=== 垃圾ProcessComplete ===");
    sys_state.processing = false;
}

// Stop conveyor并Process垃圾
void stop_and_process_garbage(garbage_type_t type) {
    ESP_LOGI(TAG, "Stop conveyor并Process垃圾Type: %d", type);

    // 先Stop conveyor
    stop_conveyor();

    // 等待传送带完全Stop
    vTaskDelay(pdMS_TO_TICKS(200));

    // 调用统一的垃圾处理函数
    process_garbage(type);
}

void process_command(char *command) {
    ESP_LOGI(TAG, "=== ProcessCommand: [%s] 长度: %d ===", command, strlen(command));

    // 立即发送确认消息
    printf("RECEIVED\r\n");
    fflush(stdout);

    if (strlen(command) == 1) {
        char cmd = command[0];

        if (cmd >= '1' && cmd <= '4') {
            // Garbage classificationCommand 1-4
            int type = cmd - '0';
            ESP_LOGI(TAG, "StartProcess垃圾Type: %d", type);
            process_garbage((garbage_type_t)type);
            printf("OK\r\n");
            fflush(stdout);
            ESP_LOGI(TAG, "CommandProcessComplete");
        } else {
            ESP_LOGW(TAG, "InvalidCommand: %s", command);
            printf("ERROR: Invalid command\r\n");
            fflush(stdout);
        }
    } else if (strlen(command) >= 2 && (command[0] == 'T' || command[0] == 't')) {
        // TCommand - 只保留核心传送带控制
        if (strcmp(command, "TRUN") == 0 || strcmp(command, "trun") == 0) {
            ESP_LOGI(TAG, "Start梯形加速到匀速持续运行");
            start_conveyor_trapezoidal_run();
            printf("TRAPEZOIDAL_RUN_START\r\n");
            fflush(stdout);
        } else if (strcmp(command, "TRUNB") == 0 || strcmp(command, "trunb") == 0) {
            ESP_LOGI(TAG, "Start梯形加速到匀速持续运行（反转）");
            start_conveyor_trapezoidal_run_backward();
            printf("TRAPEZOIDAL_RUN_BACKWARD_START\r\n");
            fflush(stdout);
        } else if (strcmp(command, "TS") == 0 || strcmp(command, "ts") == 0) {
            ESP_LOGI(TAG, "Stop梯形加减速Run");
            stop_conveyor_trapezoidal();
            printf("TRAPEZOIDAL_STOP\r\n");
            fflush(stdout);
        } else {
            ESP_LOGW(TAG, "Invalid的TCommand: %s", command);
            printf("ERROR: Invalid T command\r\n");
            fflush(stdout);
        }
    } else if (strcmp(command, "LIGHT_ON") == 0 || strcmp(command, "light_on") == 0) {
        // 开启补光灯
        control_light(true);
        printf("LIGHT_ON_OK\r\n");
        fflush(stdout);
    } else if (strcmp(command, "LIGHT_OFF") == 0 || strcmp(command, "light_off") == 0) {
        // 关闭补光灯
        control_light(false);
        printf("LIGHT_OFF_OK\r\n");
        fflush(stdout);
    } else if (strcmp(command, "STATUS") == 0 || strcmp(command, "status") == 0) {
        // 发送系统状态
        printf("STATUS:BUTTON=%d,DROP=%d,FULL=%d,LIGHT=%d,M1=%d\r\n",
               sys_state.start_button_pressed ? 1 : 0,
               sys_state.drop_detected ? 1 : 0,
               sys_state.full_detected ? 1 : 0,
               sys_state.light_on ? 1 : 0,
               sys_state.m1_auto_running ? 1 : 0);
        fflush(stdout);
    } else if (strcmp(command, "LON") == 0 || strcmp(command, "lon") == 0) {
        // 简化的开启补光灯命令
        control_light(true);
        printf("LIGHT_ON_OK\r\n");
        fflush(stdout);
    } else if (strcmp(command, "LOFF") == 0 || strcmp(command, "loff") == 0) {
        // 简化的关闭补光灯命令
        control_light(false);
        printf("LIGHT_OFF_OK\r\n");
        fflush(stdout);
    } else if (strcmp(command, "LT") == 0 || strcmp(command, "lt") == 0) {
        // 补光灯切换命令
        control_light(!sys_state.light_on);
        printf("LIGHT_TOGGLE_OK\r\n");
        fflush(stdout);
    } else if (strcmp(command, "M1ON") == 0 || strcmp(command, "m1on") == 0) {
        // 启动M1电机自动运行
        if (!sys_state.m1_auto_running) {
            start_m1_auto_running();
            ESP_LOGI(TAG, "🚀 M1ON命令触发，启动M1电机自动运行");
            printf("M1_AUTO_START_OK\r\n");
        } else {
            ESP_LOGW(TAG, "M1电机已在自动运行模式");
            printf("M1_ALREADY_RUNNING\r\n");
        }
        fflush(stdout);
    } else if (strcmp(command, "M1OFF") == 0 || strcmp(command, "m1off") == 0) {
        // 停止M1电机自动运行
        if (sys_state.m1_auto_running) {
            stop_m1_auto_running();
            ESP_LOGI(TAG, "🔴 M1OFF命令触发，停止M1电机自动运行");
            printf("M1_AUTO_STOP_OK\r\n");
        } else {
            ESP_LOGW(TAG, "M1电机未在自动运行模式");
            printf("M1_NOT_RUNNING\r\n");
        }
        fflush(stdout);
    } else if (strcmp(command, "M1FAST") == 0 || strcmp(command, "m1fast") == 0) {
        // 初赛模式：M1快速运行
        start_m1_preliminary_mode();
        ESP_LOGI(TAG, "🚀 M1FAST命令触发，启动初赛模式（快速运行）");
        printf("M1_PRELIMINARY_OK\r\n");
        fflush(stdout);
    } else if (strcmp(command, "M1SLOW") == 0 || strcmp(command, "m1slow") == 0) {
        // 决赛模式：M1标准速度运行
        start_m1_final_mode();
        ESP_LOGI(TAG, "🏁 M1SLOW命令触发，启动决赛模式（标准速度+自动控制）");
        printf("M1_FINAL_OK\r\n");
        fflush(stdout);
    } else if (strcmp(command, "M1RESTART") == 0 || strcmp(command, "m1restart") == 0) {
        // 决赛模式下重启M1（分拣完成后使用）
        if (sys_state.competition_mode == COMPETITION_MODE_FINAL && sys_state.m1_auto_running) {
            ESP_LOGI(TAG, "🔄 M1RESTART命令触发，决赛模式重启M1");
            sys_state.m1_running = true;
            control_m1_motor(true);
            printf("M1_RESTART_OK\r\n");
        } else if (sys_state.competition_mode != COMPETITION_MODE_FINAL) {
            ESP_LOGW(TAG, "M1RESTART命令仅在决赛模式下有效");
            printf("M1_RESTART_ERROR_NOT_FINAL_MODE\r\n");
        } else {
            ESP_LOGW(TAG, "M1未在自动运行模式，无法重启");
            printf("M1_RESTART_ERROR_NOT_AUTO_MODE\r\n");
        }
        fflush(stdout);
    } else {
        ESP_LOGW(TAG, "InvalidCommand格式: %s", command);
        printf("ERROR: Invalid format\r\n");
        fflush(stdout);
    }
}

void usb_serial_jtag_task(void *pvParameters) {
    char input_buffer[64];
    int buffer_index = 0;
    uint8_t buf[USB_SERIAL_JTAG_RX_BUFSIZE + 1];

    ESP_LOGI(TAG, "USB Serial JTAGTaskStart，Start监听USB数据...");

    // 等待一段时间让USBConnection稳定
    vTaskDelay(pdMS_TO_TICKS(1000));

    // 发送系统Ready消息
    printf("SYSTEM_READY\r\n");
    fflush(stdout);

    ESP_LOGI(TAG, "USB Serial JTAGConnection已Established，系统Ready");

    for(;;) {
        // 检测启动按键
        if (check_start_button()) {
            // 发送启动信号给上位机
            printf("GPIO41_BUTTON_PRESSED\r\n");
            fflush(stdout);

            // 启动M1自动运行模式
            if (!sys_state.m1_auto_running) {
                start_m1_auto_running();
                ESP_LOGI(TAG, "🚀 GPIO41按键触发，启动M1电机自动运行");
            }
        }

        // 检测掉落传感器
        if (check_drop_sensor()) {
            // 发送掉落检测信号给上位机
            printf("DROP_DETECTED\r\n");
            fflush(stdout);
        }

        // 检测满载传感器
        if (check_full_sensor()) {
            // 发送满载检测信号给上位机
            printf("FULL_DETECTED\r\n");
            fflush(stdout);
        }

        // 读取USB Serial JTAG数据
        int rx_size = usb_serial_jtag_read_bytes(buf, USB_SERIAL_JTAG_RX_BUFSIZE, pdMS_TO_TICKS(50));

        if (rx_size > 0) {
            buf[rx_size] = '\0';
            ESP_LOGI(TAG, "接收到 %d 字节USB数据: %s", rx_size, buf);

            // Process接收到的数据
            for (int i = 0; i < rx_size; i++) {
                char c = buf[i];

                if (c == '\n' || c == '\r') {
                    if (buffer_index > 0) {
                        input_buffer[buffer_index] = '\0';
                        ESP_LOGI(TAG, "Process完整Command: %s", input_buffer);
                        process_command(input_buffer);
                        buffer_index = 0;
                    }
                } else if (c >= 32 && c <= 126 && buffer_index < sizeof(input_buffer) - 1) {
                    // 只接受可打印字符
                    input_buffer[buffer_index++] = c;
                }
            }
        }

        vTaskDelay(pdMS_TO_TICKS(10));
    }

    vTaskDelete(NULL);
}

// ========================================
// M1步进电机自动控制功能
// ========================================

/**
 * 启动M1自动运行模式
 */
void start_m1_auto_running(void) {
    ESP_LOGI(TAG, "启动M1步进电机自动运行模式");
    
    sys_state.m1_auto_running = true;
    sys_state.m1_running = true;
    sys_state.drop_detected_last_state = gpio_get_level(DROP_DETECT_PIN);
    
    // 启用M1步进电机
    control_m1_motor(true);
    
    ESP_LOGI(TAG, "M1自动运行模式已启动 - 将持续运行直到掉落检测");
}

/**
 * 停止M1自动运行模式
 */
void stop_m1_auto_running(void) {
    ESP_LOGI(TAG, "停止M1步进电机自动运行模式");
    
    sys_state.m1_auto_running = false;
    sys_state.m1_running = false;
    
    // 禁用M1步进电机
    control_m1_motor(false);
    
    ESP_LOGI(TAG, "M1自动运行模式已停止");
}

/**
 * 设置比赛模式
 * @param mode 比赛模式
 */
void set_competition_mode(competition_mode_t mode) {
    sys_state.competition_mode = mode;
    
    // 根据比赛模式设置M1速度参数
    switch (mode) {
        case COMPETITION_MODE_PRELIMINARY:
            sys_state.m1_speed_delay_us = M1_SPEED_DELAY_US_FAST;
            ESP_LOGI(TAG, "设置为初赛模式 - M1快速运行 (%lu微秒延时)", sys_state.m1_speed_delay_us);
            break;
        case COMPETITION_MODE_FINAL:
            sys_state.m1_speed_delay_us = M1_SPEED_DELAY_US_SLOW;
            ESP_LOGI(TAG, "设置为决赛模式 - M1标准运行 (%lu微秒延时)", sys_state.m1_speed_delay_us);
            break;
        default:
            sys_state.m1_speed_delay_us = M1_SPEED_DELAY_US_SLOW;
            ESP_LOGI(TAG, "设置为默认模式 - M1标准运行 (%lu微秒延时)", sys_state.m1_speed_delay_us);
            break;
    }
}

/**
 * 启动M1初赛模式（快速运行）
 */
void start_m1_preliminary_mode(void) {
    ESP_LOGI(TAG, "启动M1初赛模式 - 快速运行");
    
    // 先停止当前运行
    if (sys_state.m1_auto_running) {
        stop_m1_auto_running();
        vTaskDelay(pdMS_TO_TICKS(100));
    }
    
    // 设置初赛模式
    set_competition_mode(COMPETITION_MODE_PRELIMINARY);
    
    // 启动自动运行
    start_m1_auto_running();
    
    ESP_LOGI(TAG, "初赛模式已启动 - M1将以快速度运行");
}

/**
 * 启动M1决赛模式（标准速度+自动控制）
 */
void start_m1_final_mode(void) {
    ESP_LOGI(TAG, "启动M1决赛模式 - 标准速度+自动控制");
    
    // 先停止当前运行
    if (sys_state.m1_auto_running) {
        stop_m1_auto_running();
        vTaskDelay(pdMS_TO_TICKS(100));
    }
    
    // 设置决赛模式
    set_competition_mode(COMPETITION_MODE_FINAL);
    
    // 启动自动运行
    start_m1_auto_running();
    
    ESP_LOGI(TAG, "决赛模式已启动 - M1将以标准速度运行，检测到掉落后自动控制");
}

/**
 * 控制M1电机启停
 * @param enable true=启用电机, false=禁用电机
 */
void control_m1_motor(bool enable) {
    if (enable) {
        // 启用M1步进电机（低电平启用）
        gpio_set_level(STEPPER1_EN_PIN, 0);
        // 设置默认方向（正转）
        gpio_set_level(STEPPER1_DIR_PIN, 1);
        ESP_LOGI(TAG, "M1步进电机已启用");
    } else {
        // 禁用M1步进电机（高电平禁用）
        gpio_set_level(STEPPER1_EN_PIN, 1);
        gpio_set_level(STEPPER1_STEP_PIN, 0);
        ESP_LOGI(TAG, "M1步进电机已禁用");
    }
}

/**
 * M1自动控制任务 - 持续运行直到检测到掉落
 */
void m1_auto_control_task(void *pvParameters) {
    ESP_LOGI(TAG, "M1自动控制任务启动");
    
    uint32_t step_counter = 0;
    
    for(;;) {
        // 检查是否需要运行
        if (sys_state.m1_auto_running && sys_state.m1_running) {
            // 检查掉落传感器状态
            bool current_drop_state = gpio_get_level(DROP_DETECT_PIN);
            
            // 检测掉落传感器上升沿（从低到高，表示检测到物体掉落）
            if (sys_state.drop_detected_last_state == 0 && current_drop_state == 1) {
                ESP_LOGI(TAG, "🔴 M1检测到掉落信号（上升沿） - 立即停止M1运行");
                
                // 立即停止M1运行但保持自动模式
                sys_state.m1_running = false;
                control_m1_motor(false);
                
                // 发送掉落检测消息给上位机
                printf("M1_DROP_DETECTED\r\n");
                fflush(stdout);
                
                // 根据比赛模式进行不同的后续处理
                if (sys_state.competition_mode == COMPETITION_MODE_FINAL) {
                    // 决赛模式：等待分拣完成信号，不自动重启M1
                    ESP_LOGI(TAG, "决赛模式 - 等待分拣完成信号，M1停止运行");
                    // 在决赛模式下，M1停止后不自动重启，等待上位机控制
                } else {
                    // 初赛模式：等待短时间后自动重启M1
                    ESP_LOGI(TAG, "初赛模式 - 2秒后自动重启M1");
                    vTaskDelay(pdMS_TO_TICKS(2000));  // 等待2秒
                    
                    if (sys_state.m1_auto_running) {
                        ESP_LOGI(TAG, "🟢 初赛模式 - 重新启动M1运行");
                        sys_state.m1_running = true;
                        control_m1_motor(true);
                    }
                }
            }
            
            sys_state.drop_detected_last_state = current_drop_state;
            
            // 如果M1正在运行，发送步进脉冲（使用动态速度）
            if (sys_state.m1_running) {
                gpio_set_level(STEPPER1_STEP_PIN, 1);
                esp_rom_delay_us(STEPPER_PULSE_WIDTH_US);
                gpio_set_level(STEPPER1_STEP_PIN, 0);
                esp_rom_delay_us(sys_state.m1_speed_delay_us);  // 使用动态速度参数
                
                step_counter++;
                
                // 每10000步输出一次状态
                if (step_counter % 10000 == 0) {
                    ESP_LOGI(TAG, "M1运行状态: 步数=%lu, 掉落检测引脚=%d", 
                             step_counter, current_drop_state);
                }
            } else {
                // M1停止时短暂延时
                vTaskDelay(pdMS_TO_TICKS(10));
            }
        } else {
            // 自动模式未启用时，任务休眠
            vTaskDelay(pdMS_TO_TICKS(100));
            step_counter = 0;
        }
    }
    
    vTaskDelete(NULL);
}

// Conveyor控制Task - 支持普通模式和梯形加减速模式
void conveyor_control_task(void *pvParameters) {
    ESP_LOGI(TAG, "Conveyor control task started");
    uint32_t debug_counter = 0;

    for(;;) {
        // 检查是否有任何传送带运行模式
        if (sys_state.conveyor_running || sys_state.trapezoidal_running) {
            trapezoidal_profile_t *profile = &sys_state.stepper_profile;

            // 每5000次循环输出一次调试信息，减少栈使用
            if (debug_counter % 5000 == 0) {
                ESP_LOGI(TAG, "Task: conveyor=%d, trapezoidal=%d, continuous=%d, is_running=%d, speed=%.1f, step=%lu",
                         sys_state.conveyor_running, sys_state.trapezoidal_running, profile->continuous_mode,
                         profile->is_running, profile->current_speed, profile->segment_step);
            }
            debug_counter++;

            if (sys_state.trapezoidal_running && profile->continuous_mode && profile->is_running) {
                // 梯形加减速持续Run模式
                update_continuous_trapezoidal_profile();

                // 发送步进脉冲
                gpio_set_level(STEPPER2_STEP_PIN, 1);
                esp_rom_delay_us(STEPPER_PULSE_WIDTH_US);
                gpio_set_level(STEPPER2_STEP_PIN, 0);

                // 使用计算的梯形加减速间隔
                uint32_t delay_us = profile->step_interval_us;
                if (delay_us > STEPPER_PULSE_WIDTH_US) {
                    uint32_t remaining_delay = delay_us - STEPPER_PULSE_WIDTH_US;
                    esp_rom_delay_us(remaining_delay);
                } else {
                    esp_rom_delay_us(100);  // 最小延时100微秒
                }

                // 强制不让出CPU时间，保持连续运行
                // 不调用vTaskDelay，让步进脉冲连续发送

            } else if (sys_state.conveyor_running) {
                // 普通固定速度模式
                gpio_set_level(STEPPER2_STEP_PIN, 1);
                esp_rom_delay_us(STEPPER_PULSE_WIDTH_US);
                gpio_set_level(STEPPER2_STEP_PIN, 0);
                esp_rom_delay_us(STEPPER_SPEED_DELAY_US);
            }
        } else {
            // 所有ConveyorStop时，Task休眠更长时间
            vTaskDelay(pdMS_TO_TICKS(10));
        }
    }

    vTaskDelete(NULL);
}