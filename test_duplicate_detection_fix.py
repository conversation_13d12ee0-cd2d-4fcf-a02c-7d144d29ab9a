#!/usr/bin/env python3
"""
测试重复检测处理修复
验证检测结果不会被重复处理
"""

import time
import threading
from unittest.mock import Mock, MagicMock

def test_detection_processing_logic():
    """测试检测处理逻辑"""
    print("🧪 测试重复检测处理修复")
    print("=" * 50)
    
    # 模拟GUI客户端的关键属性和方法
    class MockGUIClient:
        def __init__(self):
            self.detection_active = True
            self.video_mode = "detection"
            self.detection_processing = False
            self.last_processed_detection_id = None
            self.last_detection = None
            self.last_detection_time = 0
            self.config = {
                'detection': {
                    'confidence_threshold': 0.4
                }
            }
            
            # 计数器
            self.handle_detection_calls = 0
            self.log_messages = []
            
        def log_message(self, message):
            """模拟日志记录"""
            self.log_messages.append(message)
            print(f"[LOG] {message}")
            
        def handle_detection_results(self, results):
            """模拟检测结果处理"""
            self.handle_detection_calls += 1
            print(f"🎯 handle_detection_results 被调用 (第{self.handle_detection_calls}次)")
            
            if not results or len(results) == 0:
                return
            
            # 获取置信度最高的检测结果
            best_detection = max(results, key=lambda x: x.get('confidence', 0.0))
            confidence = best_detection.get('confidence', 0.0)
            
            # 检查置信度阈值
            if confidence < self.config['detection']['confidence_threshold']:
                return
            
            # 生成检测ID
            bbox = best_detection.get('bbox', [])
            class_name = best_detection.get('class_name', 'unknown')
            if len(bbox) == 4:
                detection_id = f"{class_name}_{bbox[0]}_{bbox[1]}_{int(confidence*100)}"
            else:
                detection_id = f"{class_name}_{int(confidence*100)}"
            
            # 避免重复处理同一个检测结果
            if detection_id == self.last_processed_detection_id:
                print(f"⚠️  重复检测ID，跳过处理: {detection_id}")
                return
            
            # 标记正在处理
            self.detection_processing = True
            self.last_processed_detection_id = detection_id
            self.last_detection_time = time.time()
            
            print(f"✅ 处理新检测: {class_name}, 置信度: {confidence:.2f}, ID: {detection_id}")
            
            # 模拟处理时间
            time.sleep(0.1)
            
            # 重置处理状态
            self.detection_processing = False
            
        def process_detection_frame(self, frame):
            """模拟帧处理（修复后的版本）"""
            if self.video_mode == "detection" and self.detection_active:
                # 模拟检测结果
                results = [
                    {
                        'bbox': [100, 200, 140, 230],
                        'confidence': 0.85,
                        'class_name': 'harmful',
                        'timestamp': time.time()
                    }
                ]
                
                if results:
                    self.log_message(f"检测到 {len(results)} 个对象")
                    # 更新last_detection供监控使用，但不在这里处理检测结果
                    self.last_detection = results
                    
        def monitor_detection_results(self):
            """模拟检测结果监控"""
            if self.detection_active and self.video_mode == "detection":
                # 获取最新检测结果
                detection = self.last_detection
                if detection and not self.detection_processing:
                    # 模拟从检测器获取的单个检测结果
                    if isinstance(detection, list) and len(detection) > 0:
                        detection = detection[0]  # 取第一个
                    
                    # 检查是否是新的检测结果
                    detection_id = f"{detection.get('class_id', 0)}_{detection.get('confidence', 0):.3f}_{detection.get('timestamp', time.time())}"
                    
                    if detection_id != self.last_processed_detection_id:
                        self.log_message(f"监控到新检测结果: {detection.get('class_name', '未知')} (置信度: {detection.get('confidence', 0):.2f})")
                        self.handle_detection_results([detection])
    
    # 创建模拟客户端
    client = MockGUIClient()
    
    print("📋 测试场景1: 单次检测处理")
    print("-" * 30)
    
    # 模拟帧处理
    client.process_detection_frame(None)
    
    # 模拟监控检查
    client.monitor_detection_results()
    
    print(f"结果: handle_detection_results 被调用 {client.handle_detection_calls} 次")
    if client.handle_detection_calls == 1:
        print("✅ 单次检测正确处理，无重复")
    else:
        print("❌ 检测处理次数异常")
    
    print(f"\n📋 测试场景2: 重复检测过滤")
    print("-" * 30)
    
    # 重置计数器
    client.handle_detection_calls = 0
    
    # 连续多次相同检测
    for i in range(3):
        client.process_detection_frame(None)
        client.monitor_detection_results()
        time.sleep(0.05)
    
    print(f"结果: 3次相同检测，handle_detection_results 被调用 {client.handle_detection_calls} 次")
    if client.handle_detection_calls == 1:
        print("✅ 重复检测正确过滤")
    else:
        print("❌ 重复检测过滤失败")
    
    print(f"\n📋 测试场景3: 不同检测分别处理")
    print("-" * 30)
    
    # 重置计数器
    client.handle_detection_calls = 0
    client.last_processed_detection_id = None
    
    # 不同的检测结果
    detections = [
        {
            'bbox': [100, 200, 140, 230],
            'confidence': 0.85,
            'class_name': 'harmful',
            'timestamp': time.time()
        },
        {
            'bbox': [300, 220, 340, 250],
            'confidence': 0.92,
            'class_name': 'kitchen',
            'timestamp': time.time() + 0.1
        },
        {
            'bbox': [500, 250, 540, 280],
            'confidence': 0.78,
            'class_name': 'recyclable',
            'timestamp': time.time() + 0.2
        }
    ]
    
    for detection in detections:
        client.last_detection = [detection]
        client.monitor_detection_results()
        time.sleep(0.2)  # 等待处理完成
    
    print(f"结果: 3个不同检测，handle_detection_results 被调用 {client.handle_detection_calls} 次")
    if client.handle_detection_calls == 3:
        print("✅ 不同检测正确分别处理")
    else:
        print("❌ 不同检测处理异常")
    
    print(f"\n📋 测试场景4: 并发处理保护")
    print("-" * 30)
    
    # 重置计数器
    client.handle_detection_calls = 0
    client.last_processed_detection_id = None
    client.detection_processing = False
    
    # 模拟并发调用
    def concurrent_detection():
        client.last_detection = [{
            'bbox': [200, 200, 240, 230],
            'confidence': 0.88,
            'class_name': 'other',
            'timestamp': time.time()
        }]
        client.monitor_detection_results()
    
    # 启动多个并发线程
    threads = []
    for i in range(5):
        thread = threading.Thread(target=concurrent_detection)
        threads.append(thread)
        thread.start()
    
    # 等待所有线程完成
    for thread in threads:
        thread.join()
    
    print(f"结果: 5个并发调用，handle_detection_results 被调用 {client.handle_detection_calls} 次")
    if client.handle_detection_calls == 1:
        print("✅ 并发处理保护正常工作")
    else:
        print("❌ 并发处理保护失败")
    
    print(f"\n🎉 重复检测处理修复测试完成！")
    print("=" * 50)
    
    # 总结
    total_logs = len(client.log_messages)
    print(f"📊 测试总结:")
    print(f"  总日志条数: {total_logs}")
    print(f"  最后处理的检测ID: {client.last_processed_detection_id}")
    
    print(f"\n💡 修复要点:")
    print("  1. ✅ 移除了process_detection_frame中的重复处理")
    print("  2. ✅ 统一使用monitor_detection_results进行检测处理")
    print("  3. ✅ 保持了检测ID去重机制")
    print("  4. ✅ 保持了并发处理保护")
    print("  5. ✅ 帧处理只负责更新last_detection，不直接处理结果")

if __name__ == "__main__":
    try:
        test_detection_processing_logic()
        
    except Exception as e:
        print(f"\n❌ 测试出错: {e}")
        import traceback
        traceback.print_exc()
