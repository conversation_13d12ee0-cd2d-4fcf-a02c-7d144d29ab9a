#!/usr/bin/env python3
"""
测试ROI区域裁剪显示功能
验证界面只显示感兴趣区域内的内容
"""

import cv2
import numpy as np
import tkinter as tk
from PIL import Image, ImageTk
import time
import logging

# 设置日志级别
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ROIDisplayTest:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("ROI区域裁剪显示测试")
        self.root.geometry("800x600")
        
        # ROI配置 - 与系统保持一致
        self.roi_area = (0, 120, 640, 360)  # 检测区域
        
        # 创建界面
        self.create_widgets()
        
        # 测试数据
        self.frame_count = 0
        self.test_running = False
        
    def create_widgets(self):
        """创建界面组件"""
        # 标题
        title_label = tk.Label(self.root, text="ROI区域裁剪显示测试", 
                              font=("Arial", 16, "bold"))
        title_label.pack(pady=10)
        
        # 信息显示
        info_frame = tk.Frame(self.root)
        info_frame.pack(pady=5)
        
        tk.Label(info_frame, text=f"ROI区域: {self.roi_area}").pack()
        tk.Label(info_frame, text="原始尺寸: 640x480 → ROI尺寸: 640x240").pack()
        
        # 视频显示区域
        self.video_label = tk.Label(self.root, text="等待视频...", 
                                   bg="black", fg="white",
                                   width=80, height=30)
        self.video_label.pack(pady=10, expand=True, fill="both")
        
        # 控制按钮
        button_frame = tk.Frame(self.root)
        button_frame.pack(pady=10)
        
        self.start_button = tk.Button(button_frame, text="开始测试", 
                                     command=self.start_test)
        self.start_button.pack(side=tk.LEFT, padx=5)
        
        self.stop_button = tk.Button(button_frame, text="停止测试", 
                                    command=self.stop_test, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=5)
        
        # 状态显示
        self.status_label = tk.Label(self.root, text="准备就绪", fg="green")
        self.status_label.pack(pady=5)
        
    def start_test(self):
        """开始测试"""
        self.test_running = True
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        self.status_label.config(text="测试运行中...", fg="blue")
        
        # 开始生成测试帧
        self.generate_test_frame()
        
    def stop_test(self):
        """停止测试"""
        self.test_running = False
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.status_label.config(text="测试已停止", fg="red")
        
    def generate_test_frame(self):
        """生成测试帧"""
        if not self.test_running:
            return
            
        try:
            # 创建640x480的测试帧
            frame = np.zeros((480, 640, 3), dtype=np.uint8)
            
            # 绘制背景网格
            self.draw_background_grid(frame)
            
            # 绘制ROI区域边界
            self.draw_roi_boundary(frame)
            
            # 绘制模拟垃圾对象
            self.draw_simulated_objects(frame)
            
            # 绘制帧信息
            self.draw_frame_info(frame)
            
            # 显示原始帧和ROI裁剪后的帧
            self.display_comparison(frame)
            
            self.frame_count += 1
            
            # 继续生成下一帧
            self.root.after(100, self.generate_test_frame)  # 10 FPS
            
        except Exception as e:
            logger.error(f"生成测试帧出错: {e}")
            self.stop_test()
    
    def draw_background_grid(self, frame):
        """绘制背景网格"""
        # 绘制垂直线
        for x in range(0, 640, 50):
            cv2.line(frame, (x, 0), (x, 480), (50, 50, 50), 1)
        
        # 绘制水平线
        for y in range(0, 480, 50):
            cv2.line(frame, (0, y), (640, y), (50, 50, 50), 1)
            
    def draw_roi_boundary(self, frame):
        """绘制ROI区域边界"""
        x1, y1, x2, y2 = self.roi_area
        
        # 绘制ROI区域框
        cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 3)
        
        # 标记ROI区域
        cv2.putText(frame, "ROI Detection Zone", (x1+10, y1-10),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
        
        # 绘制分拣区域分界线
        cv2.line(frame, (213, y1), (213, y2), (255, 255, 0), 2)
        cv2.line(frame, (427, y1), (427, y2), (255, 255, 0), 2)
        
        # 标记分拣区域
        cv2.putText(frame, "Left Zone", (50, y1+30),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)
        cv2.putText(frame, "Detection", (300, y1+30),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)
        cv2.putText(frame, "Right Zone", (500, y1+30),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)
        
    def draw_simulated_objects(self, frame):
        """绘制模拟垃圾对象"""
        # 模拟移动的垃圾对象
        t = self.frame_count * 0.1
        
        # 有害垃圾 - 在左分拣区
        x = int(100 + 50 * np.sin(t))
        y = int(200 + 30 * np.cos(t))
        cv2.rectangle(frame, (x-20, y-15), (x+20, y+15), (0, 0, 255), -1)
        cv2.putText(frame, "Harmful", (x-25, y-20),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)
        
        # 可回收垃圾 - 在右分拣区
        x = int(500 + 40 * np.sin(t + 1))
        y = int(250 + 25 * np.cos(t + 1))
        cv2.rectangle(frame, (x-20, y-15), (x+20, y+15), (0, 255, 0), -1)
        cv2.putText(frame, "Recyclable", (x-30, y-20),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
        
        # 厨余垃圾 - 在检测区移动
        x = int(320 + 80 * np.sin(t + 2))
        y = int(240 + 40 * np.cos(t + 2))
        cv2.rectangle(frame, (x-20, y-15), (x+20, y+15), (255, 0, 0), -1)
        cv2.putText(frame, "Kitchen", (x-25, y-20),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 1)
        
    def draw_frame_info(self, frame):
        """绘制帧信息"""
        # 在ROI区域外绘制信息
        cv2.putText(frame, f"Frame: {self.frame_count}", (10, 30),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        cv2.putText(frame, f"Original Size: 640x480", (10, 60),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)
        cv2.putText(frame, f"ROI Area: {self.roi_area}", (10, 85),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)
        
        # 在ROI区域外的底部绘制信息
        cv2.putText(frame, "Area outside ROI will be cropped", (10, 450),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)
        
    def display_comparison(self, frame):
        """显示原始帧和ROI裁剪后的对比"""
        try:
            # 裁剪到ROI区域
            x1, y1, x2, y2 = self.roi_area
            roi_frame = frame[y1:y2, x1:x2].copy()
            
            # 在ROI帧上添加裁剪信息
            cv2.putText(roi_frame, f"ROI Cropped: {x2-x1}x{y2-y1}", (10, 30),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
            cv2.putText(roi_frame, "Only ROI content shown", (10, 60),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 1)
            
            # 转换颜色空间
            roi_frame = cv2.cvtColor(roi_frame, cv2.COLOR_BGR2RGB)
            
            # 转换为tkinter图像
            pil_image = Image.fromarray(roi_frame)
            tk_image = ImageTk.PhotoImage(pil_image)
            
            # 更新显示
            self.video_label.config(image=tk_image, text="")
            self.video_label.image = tk_image
            
            # 更新状态
            self.status_label.config(
                text=f"帧 {self.frame_count}: 显示ROI区域 {x2-x1}x{y2-y1}",
                fg="blue"
            )
            
        except Exception as e:
            logger.error(f"显示对比出错: {e}")
    
    def run(self):
        """运行测试"""
        print("🧪 ROI区域裁剪显示测试")
        print("=" * 50)
        print(f"ROI区域: {self.roi_area}")
        print(f"原始尺寸: 640x480")
        print(f"ROI尺寸: {self.roi_area[2]-self.roi_area[0]}x{self.roi_area[3]-self.roi_area[1]}")
        print("点击'开始测试'按钮开始...")
        
        self.root.mainloop()

if __name__ == "__main__":
    try:
        test = ROIDisplayTest()
        test.run()
        
    except KeyboardInterrupt:
        print("\n⏹️  测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试出错: {e}")
        import traceback
        traceback.print_exc()
