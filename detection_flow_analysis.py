#!/usr/bin/env python3
"""
完整的检测处理流程分析
分析从系统启动到垃圾分拣完成的整个流程
"""

def analyze_detection_flow():
    """分析完整的检测处理流程"""
    print("🔍 垃圾分拣系统 - 完整检测处理流程分析")
    print("=" * 60)
    
    print("📋 1. 系统启动流程")
    print("-" * 30)
    print("start_system() 被调用:")
    print("  ├─ 设置 detection_active = True")
    print("  ├─ 重置所有状态标志")
    print("  ├─ 启动M1电机 (初赛模式快速运行)")
    print("  └─ 调用 switch_to_detection()")
    print()
    
    print("switch_to_detection() 执行:")
    print("  ├─ 设置 video_mode = 'detection'")
    print("  ├─ 启动RKNN检测器: detector.start_detection()")
    print("  ├─ 设置帧回调: set_frame_callback(process_detection_frame)")
    print("  ├─ 启动摄像头: client.start_camera()")
    print("  └─ 启动检测监控: start_detection_monitoring()")
    print()
    
    print("📋 2. 检测处理双路径")
    print("-" * 30)
    print("路径A - 帧处理 (每帧调用):")
    print("  process_detection_frame(frame)")
    print("  ├─ 调用 detector.detect_frame(frame)")
    print("  ├─ 绘制检测结果到帧")
    print("  ├─ 更新 self.last_detection = results")
    print("  └─ 更新视频显示 (仅显示，不处理业务逻辑)")
    print()
    
    print("路径B - 检测监控 (每500ms调用):")
    print("  monitor_detection_results()")
    print("  ├─ 获取 detector.get_last_detection()")
    print("  ├─ 生成检测ID (基于class_name + bbox + confidence)")
    print("  ├─ 检查是否为新检测 (与last_processed_detection_id比较)")
    print("  └─ 如果是新检测，调用 handle_detection_results()")
    print()
    
    print("📋 3. 检测结果处理流程")
    print("-" * 30)
    print("handle_detection_results(results):")
    print("  ├─ 获取置信度最高的检测结果")
    print("  ├─ 检查置信度阈值 (>= 0.4)")
    print("  ├─ 生成检测ID防重复")
    print("  ├─ 设置 detection_processing = True")
    print("  ├─ 映射类别名称和垃圾ID")
    print("  └─ 调用 auto_sort_garbage(garbage_id, display_name)")
    print()
    
    print("📋 4. 自动分拣流程")
    print("-" * 30)
    print("auto_sort_garbage(garbage_id, display_name):")
    print("  ├─ 检查是否已在分拣中 (决赛模式)")
    print("  ├─ 停止M1电机 (决赛模式)")
    print("  ├─ 获取当前垃圾位置")
    print("  ├─ 确定目标区域:")
    print("  │   ├─ recyclable/kitchen → 右分拣区 (TRUN)")
    print("  │   └─ harmful/other → 左分拣区 (TRUNB)")
    print("  ├─ 判断传送带动作:")
    print("  │   ├─ already_in_target → 直接分拣")
    print("  │   └─ move_required → 启动传送带移动")
    print("  └─ 执行相应动作")
    print()
    
    print("📋 5. 位置监控流程 (如需移动)")
    print("-" * 30)
    print("start_position_monitoring():")
    print("  ├─ 设置监控参数 (garbage_id, target_zone等)")
    print("  ├─ 启动传送带命令 (TRUN/TRUNB)")
    print("  ├─ 立即重置 detection_processing = False")
    print("  └─ 开始 monitor_position() 循环")
    print()
    
    print("monitor_position() (每200ms):")
    print("  ├─ 检查超时 (10秒)")
    print("  ├─ 获取当前位置")
    print("  ├─ 检查是否到达目标区域")
    print("  ├─ 如果到达 → execute_immediate_sorting()")
    print("  └─ 如果未到达 → 继续监控")
    print()
    
    print("📋 6. 立即分拣执行")
    print("-" * 30)
    print("execute_immediate_sorting():")
    print("  ├─ 发送停止传送带命令: TS")
    print("  ├─ 等待1秒传送带停止")
    print("  └─ 调用 _execute_sorting_action()")
    print()
    
    print("_execute_sorting_action():")
    print("  ├─ 发送分拣命令: str(garbage_id)")
    print("  │   ├─ harmful → '2'")
    print("  │   ├─ recyclable → '1'")
    print("  │   ├─ kitchen → '3'")
    print("  │   └─ other → '4'")
    print("  ├─ 等待2秒ESP32完成动作")
    print("  └─ 调用 complete_sorting_task()")
    print()
    
    print("📋 7. 任务完成处理")
    print("-" * 30)
    print("complete_sorting_task():")
    print("  ├─ 初赛模式:")
    print("  │   ├─ reset_detection_state()")
    print("  │   └─ reset_sorting_state()")
    print("  └─ 决赛模式:")
    print("      ├─ reset_sorting_state()")
    print("      └─ restart_m1_after_sorting()")
    print()
    
    print("📋 8. 状态重置")
    print("-" * 30)
    print("reset_detection_state():")
    print("  ├─ detection_processing = False")
    print("  ├─ last_processed_detection_id = None")
    print("  └─ 准备处理下一个垃圾")
    print()
    
    print("reset_sorting_state():")
    print("  ├─ detection_processing = False")
    print("  ├─ sorting_in_progress = False")
    print("  ├─ belt_running = False")
    print("  └─ stop_position_monitoring()")
    print()

def analyze_harmful_garbage_example():
    """分析有害垃圾的具体处理示例"""
    print("🧪 有害垃圾检测处理示例")
    print("=" * 40)
    
    print("假设检测到有害垃圾 (harmful, 置信度: 0.85):")
    print()
    
    print("1️⃣ 检测阶段:")
    print("  ├─ RKNN检测器识别: class_name='harmful', confidence=0.85")
    print("  ├─ 生成检测ID: 'harmful_100_200_85'")
    print("  └─ 通过置信度阈值检查 (0.85 >= 0.4)")
    print()
    
    print("2️⃣ 映射阶段:")
    print("  ├─ display_name = '有害垃圾'")
    print("  ├─ garbage_id = 2")
    print("  └─ class_name = 'harmful'")
    print()
    
    print("3️⃣ 分拣决策:")
    print("  ├─ 有害垃圾 → 左分拣区")
    print("  ├─ 传送带命令: TRUNB (倒转)")
    print("  └─ ESP32分拣命令: '2'")
    print()
    
    print("4️⃣ 执行流程:")
    print("  ├─ 获取当前位置 (假设在检测区)")
    print("  ├─ 需要移动到左分拣区")
    print("  ├─ 发送 TRUNB 启动传送带")
    print("  ├─ 立即重置 detection_processing = False")
    print("  ├─ 开始位置监控 (每200ms)")
    print("  ├─ 检测到到达左分拣区")
    print("  ├─ 发送 TS 停止传送带")
    print("  ├─ 等待1秒")
    print("  ├─ 发送 '2' 执行有害垃圾分拣")
    print("  ├─ 等待2秒ESP32完成动作")
    print("  └─ 重置所有状态，准备下一次检测")
    print()

def analyze_potential_issues():
    """分析潜在的问题点"""
    print("⚠️  潜在问题分析")
    print("=" * 30)
    
    print("1. 重复处理检查:")
    print("  ✅ process_detection_frame 只更新显示，不处理业务逻辑")
    print("  ✅ monitor_detection_results 负责统一处理")
    print("  ✅ 检测ID去重机制防止重复处理")
    print("  ✅ detection_processing 标志防止并发处理")
    print()
    
    print("2. 状态管理检查:")
    print("  ✅ detection_processing 在处理开始时设置为True")
    print("  ✅ 传送带启动后立即重置为False (允许继续检测)")
    print("  ✅ 分拣完成后完全重置状态")
    print("  ✅ 异常情况下也会重置状态")
    print()
    
    print("3. 时序控制检查:")
    print("  ✅ 使用非阻塞的 root.after() 而非 time.sleep()")
    print("  ✅ 位置监控每200ms检查一次")
    print("  ✅ 检测监控每500ms检查一次")
    print("  ✅ 适当的等待时间 (传送带停止1秒，ESP32动作2秒)")
    print()
    
    print("4. 错误处理检查:")
    print("  ✅ 所有关键方法都有try-except包装")
    print("  ✅ 异常时会重置相关状态")
    print("  ✅ 超时保护机制 (位置监控10秒超时)")
    print("  ✅ 命令响应检查 (检查OK响应)")
    print()

def main():
    """主分析函数"""
    analyze_detection_flow()
    print()
    analyze_harmful_garbage_example()
    print()
    analyze_potential_issues()
    
    print("🎯 总结")
    print("=" * 20)
    print("✅ 检测处理流程设计合理，职责分离清晰")
    print("✅ 重复处理问题已修复，每次检测只处理一次")
    print("✅ 状态管理完善，支持连续检测和分拣")
    print("✅ 错误处理和超时保护机制完备")
    print("✅ 初赛模式下，每个垃圾检测都会正确触发一次分拣动作")

if __name__ == "__main__":
    main()
