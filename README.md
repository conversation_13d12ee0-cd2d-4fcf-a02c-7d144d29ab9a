# 智能垃圾分类系统

基于ESP32和YOLO的智能垃圾分类系统，支持实时检测和自动分拣。

## 系统特性

- **智能检测**: 使用YOLOv5深度学习模型识别4类垃圾
- **实时分拣**: ESP32控制传送带和分拣机制
- **友好界面**: Tkinter图形界面，支持视频播放和实时监控
- **数据记录**: SQLite数据库存储检测记录和统计信息
- **比赛模式**: 支持垃圾分拣比赛功能
- **性能优化**: 基于yolo_test.py的高效检测实现

## 垃圾分类

1. **可回收垃圾** - 纸类、塑料、金属等
2. **有害垃圾** - 电池、灯管、药品等  
3. **厨余垃圾** - 食物残渣、果皮等
4. **其他垃圾** - 不属于以上三类的垃圾

## 系统架构

```
┌─────────────────┐    串口通信    ┌──────────────┐
│   PyQt GUI      │◄──────────────►│   ESP32S3    │
│   主控制界面     │                │   硬件控制    │
└─────────────────┘                └──────────────┘
         │
         ▼
┌─────────────────┐
│   YOLO检测模块   │
│   垃圾分类识别   │
└─────────────────┘
```

## 目录结构

```
智能垃圾分类系统/
├── main.py                          # 主程序入口
├── yolo_test.py                     # YOLO检测测试程序
├── setup.py                         # 安装脚本
├── requirements.txt                 # 依赖包列表
├── config.json                      # 系统配置文件
├── config.example.json              # 配置文件模板
├── src/                             # 核心源代码
│   └── garbage_sorter/              # 垃圾分拣系统模块
│       ├── __init__.py              # 包初始化
│       ├── core/                    # 核心功能模块
│       │   ├── __init__.py
│       │   ├── competition_manager.py # 比赛管理
│       │   └── sensor_manager.py    # 传感器管理
│       ├── hardware/                # 硬件控制模块
│       │   ├── __init__.py
│       │   ├── esp32_controller.py  # ESP32控制器
│       │   └── serial_client.py     # 串口通信客户端
│       ├── detection/               # AI检测模块
│       │   ├── __init__.py
│       │   ├── yolo_detector.py     # YOLO检测器（优化版）
│       │   └── models/              # AI模型文件目录
│       ├── gui/                     # 图形界面模块
│       │   ├── __init__.py
│       │   ├── gui_client.py        # 主界面（优化版）
│       │   ├── yolo_test_interface.py # YOLO测试界面
│       │   └── esp32_test_interface.py # ESP32测试界面
│       ├── automation/              # 自动化控制模块
│       │   ├── __init__.py
│       │   └── automation_controller.py # 自动化控制器
│       ├── simulation/              # 模拟器模块
│       │   ├── __init__.py
│       │   └── esp32_simulator.py   # ESP32模拟器
│       ├── models/                  # AI模型文件
│       │   ├── __init__.py
│       │   ├── 1.pt                 # 训练模型
│       │   ├── yolov5s.pt           # YOLOv5s模型
│       │   └── yolov5s1.pt          # 自定义模型
│       └── utils/                   # 工具模块
│           ├── __init__.py
│           └── config.py            # 配置管理
├── scripts/                         # 脚本工具目录
│   ├── launcher.py                  # 完整功能启动器
│   ├── simple_launcher.py           # 简化启动器
│   ├── esp32_simulator.py           # ESP32硬件模拟器
│   ├── performance_test.py          # 性能对比测试
│   ├── run_client.py                # 客户端启动脚本
│   └── network_serial_adapter.py    # 网络串口适配器
├── yolo/                            # YOLO相关文件
│   ├── 1.pt                         # 模型文件
│   └── yolov5-6.0/                  # YOLOv5源码
├── tests/                           # 测试目录
│   ├── __init__.py
│   ├── conftest.py                  # pytest配置
│   ├── README.md                    # 测试说明
│   ├── test_serial.py               # 串口测试
│   ├── test_conveyor.py             # 传送带测试
│   ├── test_light.py                # 补光灯测试
│   └── debug_trunb.py               # 调试脚本
├── docs/                            # 文档目录
│   ├── API.md                       # API文档
│   ├── HARDWARE.md                  # 硬件说明
│   └── system_architecture_analysis.md # 系统架构分析
├── esp32/                           # ESP32固件程序
│   ├── main/                        # 主程序
│   ├── CMakeLists.txt               # 构建配置
│   ├── garbage_sorter.ino           # Arduino代码
│   └── README.md                    # ESP32说明
└── assets/                          # 资源文件
    ├── README.md                    # 资源说明
    └── promo_video.mp4              # 宣传视频
│   └── sdkconfig.defaults           # 默认配置
└── assets/                          # 资源文件
    └── README.md                    # 资源说明
```

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置系统

```bash
# 复制配置文件模板
cp config.example.json config.json

# 根据实际情况修改配置文件
# 主要修改串口配置：Windows使用COM3，Linux使用/dev/ttyUSB0
```

### 3. 运行系统

#### 启动器模式（推荐）
```bash
python main.py
```

#### 直接硬件模式
```bash
python main.py --mode hardware --port COM3
```

#### RKNN检测测试
```bash
python test_rknn_detector.py
```

### 4. 脚本工具

```bash
# ESP32模拟器
python scripts/esp32_simulator.py

# 性能测试
python scripts/performance_test.py

# 网络串口适配器
python scripts/network_serial_adapter.py
```

### 4. 准备RKNN模型

确保RKNN模型文件位于 `detect/rknn-0804.rknn`（系统已包含）

### 5. 运行程序

```bash
# 方式1：直接运行主程序
python main.py

# 方式2：使用启动脚本
python scripts/run_client.py

# 方式3：如果已安装包，可以直接使用命令
garbage-sorter        # GUI模式
garbage-sorter-cli    # 命令行模式
```

### 6. 运行测试

```bash
# 进入测试目录
cd tests

# 运行硬件测试（需要连接ESP32）
python test_serial.py      # 串口通信测试
python test_conveyor.py    # 传送带测试
python test_light.py       # 补光灯测试
```

## 通信协议

### 发送指令格式
- `CMD:1\n` - 可回收垃圾
- `CMD:2\n` - 有害垃圾  
- `CMD:3\n` - 厨余垃圾
- `CMD:4\n` - 其他垃圾

### 接收状态格式
- `STATUS:SUCCESS\n` - 动作执行成功
- `STATUS:FAIL\n` - 动作执行失败

## 开发说明

本系统采用标准的Python包结构和模块化设计，各模块职责清晰：

### 模块说明

- **core模块**: 核心业务逻辑
  - `competition_manager.py`: 比赛流程管理
  - `sensor_manager.py`: 传感器数据管理

- **hardware模块**: 硬件控制
  - `esp32_controller.py`: ESP32硬件控制器
  - `orangepi_client.py`: OrangePi客户端通信

- **detection模块**: 智能检测
  - `yolo_detector.py`: YOLO垃圾识别

- **gui模块**: 用户界面
  - `gui_client.py`: 图形用户界面

- **utils模块**: 工具函数
  - `config.py`: 配置文件管理

### 开发环境设置

```bash
# 安装开发依赖
pip install -e .[dev]

# 运行测试
pytest tests/

# 代码格式化（可选）
black src/
```

### 项目特点

- 标准Python包结构，支持pip安装
- 模块化设计，便于维护和扩展
- 完整的测试套件
- 详细的配置管理
- 支持多种运行方式

## 许可证

MIT License
